# 🎯 Explicit Storage System - YOU Control Everything

## 📋 Overview

This is a simplified, explicit storage system where **YOU** define exactly where every piece of data goes. No automatic decisions, no surprises, just clear rules that you set.

## 🚀 Super Simple Setup

### Option 1: Quick Setup (Recommended)
```typescript
// In your App.tsx - just add this
import { quickSetupBalanced } from './services/explicitStorageInit';

useEffect(() => {
  quickSetupBalanced().then(status => {
    console.log('✅ Explicit storage ready!', status);
  });
}, []);
```

### Option 2: Custom Rules
```typescript
import { initializeExplicitStorage } from './services/explicitStorageInit';

const myRules = {
  storage: {
    timelineEdits: 'localStorage',     // Fast timeline access
    sceneMetadata: 'localStorage',     // Fast scene access
    aiResults: {
      'subtitle-generator': 'localStorage',  // Small text → localStorage
      'video-enhancer': 'indexedDB',        // Larger data → IndexedDB
      'color-grader': 'indexedDB',          // Larger data → IndexedDB
      // ... define for each AI agent
    },
    videoBlobs: {
      original: 'indexedDB',    // Videos → IndexedDB
      processed: 'indexedDB',   // Processed videos → IndexedDB
      aiGenerated: 'indexedDB'  // AI videos → IndexedDB
    },
    processing: {
      'subtitle-generator': 'client',  // Fast on client
      'video-enhancer': 'client',     // Your choice: client or cloud
      // ... define for each AI agent
    }
  }
};

initializeExplicitStorage(myRules);
```

## 🎯 How to Use

### Save Timeline Edits
```typescript
import { storage } from './services/explicitStorageInit';

// Save timeline edits - goes where YOU specified in rules
await storage.saveTimelineEdits(sceneId, timelineData);

// Load timeline edits - comes from where YOU specified
const timelineData = await storage.loadTimelineEdits(sceneId);
```

### Save Scene Data
```typescript
// Save scene metadata - goes where YOU specified
await storage.saveSceneMetadata(sceneData);

// Load scene metadata - comes from where YOU specified
const sceneData = await storage.loadSceneMetadata(sceneId);
```

### Process with AI Agents
```typescript
import { processing } from './services/explicitStorageInit';

// Option 1: Use your predefined rules
const result = await processing.processSceneWithStorageRules(
  'video-enhancer',
  sceneData,
  (progress) => console.log(`${progress}%`),
  (status) => console.log(status)
);

// Option 2: Override and specify explicitly
const result = await processing.processScene(
  'video-enhancer',
  sceneData,
  'client', // Force client processing
  (progress) => console.log(`${progress}%`),
  (status) => console.log(status)
);
```

### Save AI Results
```typescript
// AI results are automatically saved according to your rules
// But you can also save manually:
await storage.saveAIResult(sceneId, 'video-enhancer', result);

// Load AI results
const result = await storage.loadAIResult(sceneId, 'video-enhancer');
```

### Save Video Blobs
```typescript
// Save video blobs according to your rules
await storage.saveVideoBlob(sceneId, 'processed', videoBlob);

// Load video blobs
const videoBlob = await storage.loadVideoBlob(sceneId, 'processed');
```

## 📋 Predefined Configurations

### 1. All localStorage (Fastest)
```typescript
import { quickSetupAllLocalStorage } from './services/explicitStorageInit';
await quickSetupAllLocalStorage();
```
- **Best for**: Small projects, fast access
- **Limitation**: ~5MB storage limit
- **Speed**: Fastest possible

### 2. Balanced (Recommended)
```typescript
import { quickSetupBalanced } from './services/explicitStorageInit';
await quickSetupBalanced();
```
- **Best for**: Most projects
- **Storage**: localStorage for metadata, IndexedDB for larger data
- **Speed**: Fast with good capacity

### 3. Cloud Heavy
```typescript
import { quickSetupCloudHeavy } from './services/explicitStorageInit';
await quickSetupCloudHeavy({
  projectId: 'your-gcp-project',
  bucketName: 'your-bucket',
  apiEndpoint: 'https://your-api.com'
});
```
- **Best for**: Large projects with cloud setup
- **Storage**: Cloud for heavy data, local for fast access
- **Speed**: Good with unlimited capacity

## 🔧 Customizing Your Rules

### Storage Rules
```typescript
const customRules = {
  storage: {
    // Timeline data
    timelineEdits: 'localStorage',    // or 'indexedDB'
    sceneMetadata: 'localStorage',    // or 'indexedDB'
    
    // AI results - define for each agent type
    aiResults: {
      'subtitle-generator': 'localStorage',  // Small text data
      'video-enhancer': 'indexedDB',        // Larger enhancement data
      'audio-processor': 'localStorage',     // Audio metadata
      'color-grader': 'indexedDB',          // Color adjustment data
      'object-detector': 'indexedDB',       // Detection results
      'content-analyzer': 'localStorage',    // Analysis text
      'auto-editor': 'localStorage',        // Edit suggestions
      'scene-classifier': 'localStorage',    // Classification data
      'transition-suggester': 'localStorage', // Transition data
      'noise-reducer': 'indexedDB'          // Audio processing data
    },
    
    // Video blobs
    videoBlobs: {
      original: 'indexedDB',      // or 'cloud'
      processed: 'indexedDB',     // or 'cloud'
      aiGenerated: 'indexedDB'    // or 'cloud'
    },
    
    // Processing strategy - where AI agents run
    processing: {
      'subtitle-generator': 'client',   // or 'cloud'
      'video-enhancer': 'client',      // or 'cloud'
      'audio-processor': 'client',     // or 'cloud'
      'color-grader': 'client',        // or 'cloud'
      'object-detector': 'client',     // or 'cloud'
      'content-analyzer': 'client',    // or 'cloud'
      'auto-editor': 'client',         // or 'cloud'
      'scene-classifier': 'client',    // or 'cloud'
      'transition-suggester': 'client', // or 'cloud'
      'noise-reducer': 'client'        // or 'cloud'
    }
  }
};
```

## 📊 Monitoring

### Check Storage Usage
```typescript
const usage = await storage.getStorageUsage();
console.log('Storage usage:', usage);
```

### Check Processing Jobs
```typescript
const stats = processing.getProcessingStats();
console.log('Processing stats:', stats);

const activeJobs = processing.getActiveJobs();
console.log('Active jobs:', activeJobs);
```

## 🎯 Integration with Existing Timeline

Your existing timeline system will work exactly the same. The explicit storage system just provides better organization and future-proofing:

```typescript
// Your existing timeline code
const handleTimelineEdit = async (sceneId, editData) => {
  // Instead of direct localStorage
  // localStorage.setItem(`timeline-${sceneId}`, JSON.stringify(editData));
  
  // Use explicit storage (goes to same place, but organized)
  await storage.saveTimelineEdits(sceneId, editData);
};

const loadTimelineData = async (sceneId) => {
  // Instead of direct localStorage
  // const data = localStorage.getItem(`timeline-${sceneId}`);
  
  // Use explicit storage
  return await storage.loadTimelineEdits(sceneId);
};
```

## ✅ Benefits

1. **Full Control**: YOU decide where everything goes
2. **No Surprises**: No automatic decisions that might go wrong
3. **Clear Rules**: Easy to understand and modify
4. **Future Ready**: Easy to add cloud storage when needed
5. **Backwards Compatible**: Works with your existing timeline system
6. **Performance**: You optimize for your specific needs

## 🚀 Adding New AI Agents

When you create a new AI agent, just add it to your rules:

```typescript
// Update your storage rules
storage.updateRules({
  aiResults: {
    ...existingRules,
    'my-new-agent': 'indexedDB'  // You decide where results go
  },
  processing: {
    ...existingRules,
    'my-new-agent': 'client'     // You decide where it processes
  }
});
```

That's it! The explicit storage system gives you complete control while maintaining the efficiency of your current localStorage timeline system.
