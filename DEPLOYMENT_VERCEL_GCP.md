# 🚀 Deployment Guide: Vercel (Frontend) + Google Cloud (Backend)

This guide will help you deploy the Insomnia video editing application with the frontend on Vercel and the backend on Google Cloud Platform.

## 📋 Prerequisites

- GitHub account
- Vercel account (free tier available)
- Google Cloud Platform account with billing enabled
- Google Cloud CLI installed locally
- Docker installed locally (for Cloud Run deployment)

## 🔧 Backend Deployment on Google Cloud

### Step 1: Prepare Backend for Google Cloud

1. **Create Dockerfile** in the backend directory:
```dockerfile
# backend/Dockerfile
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p temp_video_uploads analyzed_videos_store analysis_data_store

# Expose port
EXPOSE 8080

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

2. **Create .dockerignore** in the backend directory:
```
myenv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
.git
.gitignore
README.md
Dockerfile
.dockerignore
debug.log
```

3. **Update main.py for Google Cloud**:
```python
import os

# Update port configuration
PORT = int(os.environ.get("PORT", 8080))

# Update CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", 
        "http://127.0.0.1:5173",
        "https://*.vercel.app",
        "https://your-app-name.vercel.app",  # Replace with your Vercel domain
        "https://*.googleusercontent.com"  # For Google Cloud domains
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Update the main block
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=PORT, reload=False)
```

4. **Create requirements.txt** (if not exists):
```bash
cd backend
pip freeze > requirements.txt
```

### Step 2: Set Up Google Cloud Project

1. **Create a new Google Cloud project**:
```bash
gcloud projects create insomnia-video-editor --name="Insomnia Video Editor"
gcloud config set project insomnia-video-editor
```

2. **Enable required APIs**:
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

3. **Set up authentication**:
```bash
gcloud auth login
gcloud auth configure-docker
```

### Step 3: Deploy to Cloud Run

1. **Build and deploy using Cloud Build**:
```bash
cd backend
gcloud builds submit --tag gcr.io/insomnia-video-editor/backend
```

2. **Deploy to Cloud Run**:
```bash
gcloud run deploy insomnia-backend \
  --image gcr.io/insomnia-video-editor/backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --max-instances 10
```

3. **Set environment variables**:
```bash
gcloud run services update insomnia-backend \
  --region us-central1 \
  --set-env-vars="GEMINI_API_KEY=your-gemini-api-key"
```

4. **Get your service URL**:
```bash
gcloud run services describe insomnia-backend --region us-central1 --format 'value(status.url)'
```

### Step 4: Configure Cloud Storage (Optional)

For large file storage:

1. **Create a storage bucket**:
```bash
gsutil mb gs://insomnia-video-storage
```

2. **Set bucket permissions**:
```bash
gsutil iam ch allUsers:objectViewer gs://insomnia-video-storage
```

3. **Update backend to use Cloud Storage** (add to main.py):
```python
from google.cloud import storage

# Initialize Cloud Storage client
storage_client = storage.Client()
bucket_name = "insomnia-video-storage"

def upload_to_gcs(file_path, blob_name):
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    blob.upload_from_filename(file_path)
    return f"gs://{bucket_name}/{blob_name}"
```

## 🌐 Frontend Deployment on Vercel

### Step 1: Prepare Frontend for Production

1. **Create environment configuration** in `src/config/environment.ts`:
```typescript
// src/config/environment.ts
export const API_CONFIG = {
  development: {
    baseURL: 'http://localhost:8000',
  },
  production: {
    baseURL: 'https://insomnia-backend-xxxxx-uc.a.run.app', // Your Cloud Run URL
  }
};

export const getApiBaseUrl = (): string => {
  const isDevelopment = process.env.NODE_ENV === 'development' || 
                       window.location.hostname === 'localhost';
  
  return isDevelopment ? API_CONFIG.development.baseURL : API_CONFIG.production.baseURL;
};
```

2. **Update App.tsx to use production API**:
```typescript
// In src/App.tsx
import { getApiBaseUrl } from './config/environment';

// Replace hardcoded localhost URLs with:
const apiBaseUrl = getApiBaseUrl();

// Example usage:
const response = await fetch(`${apiBaseUrl}/api/health`);
```

3. **Create vercel.json** in the project root:
```json
{
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "https://insomnia-backend-xxxxx-uc.a.run.app/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ]
}
```

### Step 2: Deploy Frontend to Vercel

1. **Login to Vercel** and click "New Project"
2. **Import your GitHub repository**
3. **Configure the project**:
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

4. **Set Environment Variables**:
   - `VITE_API_URL`: Your Cloud Run service URL
   - `VITE_GEMINI_API_KEY`: Your Gemini API key
   - `NODE_ENV`: `production`

5. **Deploy**: Click "Deploy"

## 🔧 Advanced Google Cloud Configuration

### Cloud SQL Database (Optional)

For persistent data storage:

1. **Create Cloud SQL instance**:
```bash
gcloud sql instances create insomnia-db \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1
```

2. **Create database**:
```bash
gcloud sql databases create insomnia --instance=insomnia-db
```

3. **Update backend to use Cloud SQL**:
```python
import sqlalchemy
from sqlalchemy import create_engine

# Database connection
db_user = os.environ.get("DB_USER")
db_pass = os.environ.get("DB_PASS")
db_name = os.environ.get("DB_NAME")
cloud_sql_connection_name = os.environ.get("CLOUD_SQL_CONNECTION_NAME")

engine = create_engine(
    f"postgresql+psycopg2://{db_user}:{db_pass}@/{db_name}?host=/cloudsql/{cloud_sql_connection_name}"
)
```

### Cloud CDN (Optional)

For faster content delivery:

1. **Create a load balancer with CDN**:
```bash
gcloud compute backend-services create insomnia-backend-service --global
gcloud compute url-maps create insomnia-url-map --default-service insomnia-backend-service
gcloud compute target-https-proxies create insomnia-https-proxy --url-map insomnia-url-map
```

### Monitoring and Logging

1. **Enable Cloud Monitoring**:
```bash
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
```

2. **Add monitoring to your backend**:
```python
from google.cloud import monitoring_v3
from google.cloud import logging

# Initialize clients
monitoring_client = monitoring_v3.MetricServiceClient()
logging_client = logging.Client()
```

## 🧪 Testing Your Deployment

1. **Test Cloud Run backend**:
```bash
curl https://insomnia-backend-xxxxx-uc.a.run.app/api/health
```

2. **Test Vercel frontend**:
   - Visit your Vercel URL
   - Check browser console for errors
   - Test video upload functionality

3. **Load testing**:
```bash
# Install Apache Bench
sudo apt-get install apache2-utils

# Test backend performance
ab -n 100 -c 10 https://insomnia-backend-xxxxx-uc.a.run.app/api/health
```

## 💰 Cost Optimization

### Cloud Run Pricing Tips:
- Use minimum instances: 0 (scales to zero)
- Set appropriate memory/CPU limits
- Use request-based pricing
- Monitor usage with Cloud Monitoring

### Estimated Monthly Costs:
- **Cloud Run**: $0-50 (depending on usage)
- **Cloud Storage**: $0.02/GB
- **Cloud SQL**: $7-25 (if used)
- **Vercel**: Free tier or $20/month Pro

## 🔒 Security Best Practices

1. **Use IAM roles**:
```bash
gcloud projects add-iam-policy-binding insomnia-video-editor \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/storage.objectAdmin"
```

2. **Enable VPC connector** for private networking:
```bash
gcloud compute networks vpc-access connectors create insomnia-connector \
  --region=us-central1 \
  --subnet=default \
  --subnet-project=insomnia-video-editor
```

3. **Use Secret Manager** for API keys:
```bash
echo "your-api-key" | gcloud secrets create gemini-api-key --data-file=-
```

## 🔄 CI/CD Pipeline

Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Google Cloud

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Google Cloud
        uses: google-github-actions/setup-gcloud@v0
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: insomnia-video-editor
          
      - name: Build and Deploy
        run: |
          cd backend
          gcloud builds submit --tag gcr.io/insomnia-video-editor/backend
          gcloud run deploy insomnia-backend --image gcr.io/insomnia-video-editor/backend --region us-central1
```

Your application is now deployed on enterprise-grade Google Cloud infrastructure! 🌟
