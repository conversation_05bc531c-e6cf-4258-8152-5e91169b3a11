#!/bin/bash

# Deployment script for GCP-Only Strategy
# This script deploys both frontend and backend to Google Cloud Run

set -e

echo "🚀 Starting GCP-Only Deployment"
echo "==============================="

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-"insomnia-video-editor"}
REGION=${GCP_REGION:-"us-central1"}
BUCKET_NAME=${GCS_BUCKET_NAME:-"insomnia-video-storage"}
SERVICE_NAME="insomnia-app"

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI not found. Please install Google Cloud SDK."
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Please install Docker."
    exit 1
fi

# Authenticate with Google Cloud
echo "🔐 Authenticating with Google Cloud..."
gcloud auth login --brief
gcloud config set project $PROJECT_ID
gcloud auth configure-docker

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable storage.googleapis.com

# Create storage bucket if it doesn't exist
echo "🪣 Setting up Google Cloud Storage..."
if ! gsutil ls -b gs://$BUCKET_NAME &> /dev/null; then
    echo "Creating bucket: $BUCKET_NAME"
    gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME
    
    # Set bucket permissions for public read
    gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME
    
    # Enable CORS for the bucket
    echo '[{"origin":["*"],"method":["GET","POST","PUT","DELETE"],"responseHeader":["Content-Type","Access-Control-Allow-Origin"],"maxAgeSeconds":3600}]' > cors.json
    gsutil cors set cors.json gs://$BUCKET_NAME
    rm cors.json
else
    echo "Bucket $BUCKET_NAME already exists"
fi

# Get the expected service URL (before deployment)
SERVICE_URL="https://$SERVICE_NAME-$(echo $PROJECT_ID | tr '[:upper:]' '[:lower:]')-$REGION.a.run.app"

# Build and deploy using Cloud Build
echo "🏗️ Building and deploying full-stack application..."
gcloud builds submit \
    --config cloudbuild-fullstack.yaml \
    --substitutions=_GCS_BUCKET_NAME=$BUCKET_NAME,_REGION=$REGION,_PROJECT_HASH=$(echo $PROJECT_ID | cut -c1-5)-$(echo $REGION | cut -c1-2) \
    --timeout=1800s

echo "✅ Application deployed to: $SERVICE_URL"

# Test the deployment
echo "🧪 Testing deployment..."
sleep 10  # Wait for service to be ready

if curl -f "$SERVICE_URL/api/health" > /dev/null 2>&1; then
    echo "✅ Backend health check passed"
else
    echo "⚠️ Backend health check failed - service may still be starting"
fi

if curl -f "$SERVICE_URL/" > /dev/null 2>&1; then
    echo "✅ Frontend health check passed"
else
    echo "⚠️ Frontend health check failed - service may still be starting"
fi

echo ""
echo "🎉 GCP-Only deployment completed successfully!"
echo "============================================="
echo "Application URL: $SERVICE_URL"
echo "API Health: $SERVICE_URL/api/health"
echo "Storage Bucket: gs://$BUCKET_NAME"
echo ""
echo "Next steps:"
echo "1. Test the application functionality"
echo "2. Set up monitoring and alerts"
echo "3. Configure custom domain (optional)"
echo "4. Set up CI/CD pipeline"
echo ""

# Optional: Open the application in browser
read -p "Open application in browser? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
        open "$SERVICE_URL"
    elif command -v xdg-open &> /dev/null; then
        xdg-open "$SERVICE_URL"
    else
        echo "Please open $SERVICE_URL in your browser"
    fi
fi
