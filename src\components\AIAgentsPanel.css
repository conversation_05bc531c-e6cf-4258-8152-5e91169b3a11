/* src/components/AIAgentsPanel.css */

.ai-agents-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.ai-agents-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.ai-agents-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.api-warning {
  margin-top: 8px;
  padding: 6px 12px;
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.5);
  border-radius: 4px;
  font-size: 12px;
  color: #fff3cd;
}

/* Search */
.ai-agents-search {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* Category Filter */
.category-filter {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.category-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.category-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* AI Agents List */
.ai-agents-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.no-agents {
  text-align: center;
  padding: 32px 16px;
  color: #666;
  font-style: italic;
}

.ai-agent-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e0e0e0;
  border-left: 4px solid #667eea;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.ai-agent-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.ai-agent-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.agent-icon {
  font-size: 24px;
  margin-right: 12px;
  min-width: 32px;
  text-align: center;
}

.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.agent-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-add-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
  transition: transform 0.2s ease;
}

.ai-agent-item:hover .agent-add-btn {
  transform: scale(1.1);
}

/* Instructions */
.ai-agents-instructions {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
}

.ai-agents-instructions h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #333;
  font-weight: 600;
}

.ai-agents-instructions ol {
  margin: 0 0 16px 0;
  padding-left: 16px;
}

.ai-agents-instructions li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.edge-purpose-info {
  margin-top: 16px;
  padding: 12px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.edge-purpose-info h5 {
  margin: 0 0 8px 0;
  color: #667eea;
}

.edge-purpose-info p {
  margin: 0;
  line-height: 1.5;
  color: #555;
}

/* Drag and drop feedback */
.ai-agent-item[draggable="true"] {
  cursor: grab;
}

.ai-agent-item[draggable="true"]:active {
  cursor: grabbing;
  opacity: 0.8;
}

/* Scrollbar styling */
.ai-agents-list::-webkit-scrollbar {
  width: 6px;
}

.ai-agents-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-agents-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ai-agents-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ai-agents-header {
    padding: 12px;
  }
  
  .ai-agents-header h4 {
    font-size: 16px;
  }
  
  .ai-agent-item {
    padding: 10px;
  }
  
  .agent-icon {
    font-size: 20px;
    margin-right: 10px;
    min-width: 28px;
  }
  
  .agent-name {
    font-size: 13px;
  }
  
  .agent-description {
    font-size: 11px;
  }
  
  .category-filter {
    padding: 8px 12px;
  }
  
  .category-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}
