/* NodeTimeline Component Styles */

.node-timeline-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(20, 20, 30, 0.75));
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(12px) saturate(1.1);
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.2);
  }
}

.node-timeline-modal {
  background: linear-gradient(145deg, rgba(26, 26, 26, 0.85), rgba(42, 42, 42, 0.9));
  border-radius: 12px;
  width: 90vw;
  height: 85vh;
  max-width: 900px;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  z-index: 10001;
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  backdrop-filter: blur(20px);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.node-timeline-header {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.7), rgba(51, 51, 51, 0.8));
  height: 50px;
  min-height: 50px;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(10px);
}

.node-timeline-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.node-timeline-title {
  color: #fff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.node-timeline-header-buttons {
  display: flex;
  gap: 8px;
}

.node-timeline-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  opacity: 0.9;
}

.node-timeline-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.node-timeline-button:hover::before {
  left: 100%;
}

.node-timeline-button.save {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.8), rgba(53, 122, 189, 0.9));
  color: #fff;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.node-timeline-button.save:hover {
  background: linear-gradient(135deg, rgba(53, 122, 189, 0.9), rgba(45, 90, 160, 0.95));
  box-shadow: 0 3px 12px rgba(74, 144, 226, 0.3);
  transform: translateY(-1px);
  opacity: 1;
}

.node-timeline-button.close {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.7), rgba(85, 85, 85, 0.8));
  color: #fff;
  box-shadow: 0 2px 8px rgba(102, 102, 102, 0.2);
}

.node-timeline-button.close:hover {
  background: linear-gradient(135deg, rgba(85, 85, 85, 0.8), rgba(68, 68, 68, 0.9));
  box-shadow: 0 3px 12px rgba(102, 102, 102, 0.3);
  transform: translateY(-1px);
  opacity: 1;
}

.node-timeline-video-area {
  margin: 0 auto;
  height: 400px;
  min-height: 300px;
  max-height: 400px;
  width: 400px;
  min-width: 250px;
  max-width: 700px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  padding: 1px;
  position: relative;
  border-bottom: 1px solid #080707;
}

.node-timeline-video-container {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.node-timeline-video {
  max-width: 100%;
  max-height: 260px;
  width: auto;
  height: auto;
  background-color: #000;
  display: block;
  border-radius: 4px;
  object-fit: contain;
}

.node-timeline-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 16px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 12px 20px;
  border-radius: 4px;
}

.node-timeline-controls {
  padding: 10px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.06);
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.7), rgba(51, 51, 51, 0.8));
  display: flex;
  align-items: center;
  gap: 16px;
  height: 50px;
  min-height: 50px;
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(10px);
}

.node-timeline-controls::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.node-timeline-playback-controls {
  display: flex;
  gap: 8px;
}

.node-timeline-control-button {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

.node-timeline-control-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.node-timeline-control-button:hover::before {
  left: 100%;
}

.node-timeline-control-button.play {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.8), rgba(53, 122, 189, 0.9));
  color: #fff;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.node-timeline-control-button.play.playing {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.8), rgba(231, 76, 60, 0.9));
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  animation: playingPulse 2s infinite;
}

@keyframes playingPulse {
  0%, 100% { box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3); }
  50% { box-shadow: 0 3px 12px rgba(255, 107, 107, 0.4); }
}

.node-timeline-control-button.stop {
  background: linear-gradient(135deg, rgba(102, 102, 102, 0.7), rgba(85, 85, 85, 0.8));
  color: #fff;
  box-shadow: 0 2px 8px rgba(102, 102, 102, 0.2);
}

.node-timeline-control-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
  opacity: 1;
}

.node-timeline-tools {
  display: flex;
  gap: 8px;
}

.node-timeline-tool-button {
  padding: 6px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background: linear-gradient(135deg, rgba(85, 85, 85, 0.7), rgba(68, 68, 68, 0.8));
  color: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  opacity: 0.8;
}

.node-timeline-tool-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.node-timeline-tool-button:hover::before {
  left: 100%;
}

.node-timeline-tool-button.active {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.8), rgba(53, 122, 189, 0.9));
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
  transform: scale(1.02);
  opacity: 1;
}

.node-timeline-tool-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  opacity: 1;
}

.node-timeline-tool-button.delete {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(211, 47, 47, 0.3));
  border: 1px solid rgba(244, 67, 54, 0.4);
  color: #F44336;
  animation: deleteButtonPulse 2s infinite;
}

.node-timeline-tool-button.delete:hover {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.3), rgba(211, 47, 47, 0.4));
  border-color: rgba(244, 67, 54, 0.6);
  transform: translateY(-1px) scale(1.05);
}

@keyframes deleteButtonPulse {
  0%, 100% {
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.2);
  }
  50% {
    box-shadow: 0 3px 8px rgba(244, 67, 54, 0.3);
  }
}

.node-timeline-volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 10px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
}

.node-timeline-volume-control span {
  font-size: 14px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  opacity: 0.8;
}

.node-timeline-volume-slider {
  width: 70px;
  height: 4px;
  background: linear-gradient(to right, rgba(51, 51, 51, 0.8), rgba(85, 85, 85, 0.9));
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  -webkit-appearance: none;
  appearance: none;
  opacity: 0.8;
}

.node-timeline-volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.9), rgba(53, 122, 189, 0.95));
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.node-timeline-volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.4);
}

.node-timeline-volume-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
  transition: all 0.3s ease;
}

.node-timeline-volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.6);
}

.node-timeline-volume-slider:hover {
  background: linear-gradient(to right, #444, #666);
}

.node-timeline-volume-button {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  opacity: 0.8;
}

.node-timeline-volume-button:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.05);
  opacity: 1;
}

.node-timeline-volume-display {
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  min-width: 28px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.8;
}

.node-timeline-audio-sync-indicator {
  color: #4CAF50;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(56, 142, 60, 0.3));
  border-radius: 6px;
  border: 1px solid rgba(76, 175, 80, 0.4);
  text-align: center;
  opacity: 0.9;
  animation: audioSyncPulse 2s infinite;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

@keyframes audioSyncPulse {
  0%, 100% {
    opacity: 0.9;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
  }
  50% {
    opacity: 1;
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
  }
}

.node-timeline-video-gap-indicator {
  color: #FF9800;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(230, 126, 34, 0.3));
  border-radius: 6px;
  border: 1px solid rgba(255, 152, 0, 0.4);
  text-align: center;
  opacity: 0.9;
  animation: videoGapPulse 1.5s infinite;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.2);
}

@keyframes videoGapPulse {
  0%, 100% {
    opacity: 0.9;
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.2);
  }
  50% {
    opacity: 1;
    box-shadow: 0 3px 8px rgba(255, 152, 0, 0.3);
  }
}

.node-timeline-video-gap-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
  border-radius: 8px;
  animation: gapOverlayFadeIn 0.3s ease-out;
}

.node-timeline-gap-message {
  text-align: center;
  color: #FF9800;
  animation: gapMessagePulse 2s infinite;
}

.gap-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.8;
}

.gap-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.gap-subtext {
  font-size: 12px;
  opacity: 0.7;
  font-weight: 400;
}

@keyframes gapOverlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gapMessagePulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}



.node-timeline-timecode {
  color: #fff;
  font-size: 11px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-weight: 500;
  margin-left: auto;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8), rgba(42, 42, 42, 0.9));
  padding: 4px 10px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  letter-spacing: 0.5px;
  min-width: 80px;
  text-align: center;
  opacity: 0.9;
  backdrop-filter: blur(10px);
}

.node-timeline-area {
  height: 200px;
  min-height: 200px;
  max-height: 200px;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.9), rgba(34, 34, 34, 0.95));
  display: flex;
  border-top: 1px solid rgba(255, 255, 255, 0.06);
  flex-shrink: 0;
  position: relative;
  backdrop-filter: blur(10px);
}

.node-timeline-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.node-timeline-track-headers {
  width: 80px;
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.8), rgba(51, 51, 51, 0.9));
  border-right: 1px solid rgba(255, 255, 255, 0.06);
  position: relative;
  backdrop-filter: blur(10px);
}

.node-timeline-track-headers::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.node-timeline-track-header {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 11px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.8), rgba(51, 51, 51, 0.9));
  transition: all 0.3s ease;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
}

.node-timeline-track-header:hover {
  background: linear-gradient(135deg, rgba(51, 51, 51, 0.9), rgba(58, 58, 58, 0.95));
  transform: translateX(1px);
  opacity: 1;
}

.node-timeline-track-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, rgba(74, 144, 226, 0.8), rgba(53, 122, 189, 0.9));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.node-timeline-track-header:hover::before {
  opacity: 1;
}

.node-timeline-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.node-timeline-canvas {
  display: block;
  cursor: pointer;
  transition: cursor 0.2s ease;
}

.node-timeline-canvas.blade-tool {
  cursor: crosshair;
}

.node-timeline-canvas.hand-tool {
  cursor: grab;
}

.node-timeline-canvas.hand-tool:active {
  cursor: grabbing;
}

/* Cursor states for different interactions */
.node-timeline-canvas.trimming {
  cursor: ew-resize;
}

.node-timeline-canvas.playhead-dragging {
  cursor: ew-resize;
}

.node-timeline-canvas.moving {
  cursor: move;
}

.node-timeline-canvas.selecting {
  cursor: pointer;
}

/* Visual feedback for clip interactions */
.node-timeline-canvas:hover {
  background-color: rgba(255, 255, 255, 0.01);
}

/* Selection indicators */
.timeline-clip-selected {
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.6);
  animation: clipSelected 0.3s ease-out;
}

@keyframes clipSelected {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.8);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.6);
  }
}

/* Trim handle hover effects */
.timeline-trim-handle {
  transition: all 0.2s ease;
}

.timeline-trim-handle:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .node-timeline-modal {
    width: 95vw;
    height: 90vh;
  }

  .node-timeline-controls {
    flex-wrap: wrap;
    gap: 10px;
  }

  .node-timeline-timecode {
    margin-left: 0;
    margin-top: 8px;
  }
}

/* Animation for modal appearance */
@keyframes nodeTimelineSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.node-timeline-modal {
  animation: nodeTimelineSlideIn 0.3s ease-out;
}
