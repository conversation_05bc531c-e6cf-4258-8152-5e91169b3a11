# Google Cloud Build configuration for backend deployment
steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/insomnia-backend:$BUILD_ID', '.']
    dir: 'backend'

  # Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/insomnia-backend:$BUILD_ID']

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'insomnia-backend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/insomnia-backend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--timeout'
      - '3600'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=${_GCS_BUCKET_NAME},GCP_PROJECT_ID=$PROJECT_ID'

# Substitution variables
substitutions:
  _GCS_BUCKET_NAME: 'insomnia-video-storage'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Build timeout
timeout: '1200s'
