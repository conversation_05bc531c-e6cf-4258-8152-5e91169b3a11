# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
myenv/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Docker
Dockerfile
.dockerignore

# Logs
*.log
debug.log

# Test files
test_*.py
*_test.py

# Temporary files
temp_video_uploads/*
analyzed_videos_store/*
analysis_data_store/*
exported_videos/*

# Keep directory structure but ignore contents
!temp_video_uploads/.gitkeep
!analyzed_videos_store/.gitkeep
!analysis_data_store/.gitkeep
!exported_videos/.gitkeep
