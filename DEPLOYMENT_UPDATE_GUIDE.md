# 🚀 Insomnia Video Editor - Deployment & Update Guide

## 📋 **Quick Reference**

**Frontend URL:** https://insomniav23-k0ktmkqdx-ps-projects-5d292791.vercel.app
**Backend URL:** https://insomnia-backend-************.asia-southeast1.run.app  
**Project ID:** cryptic-lattice-463111-k6  
**Region:** asia-southeast1  
**Bucket:** insomnia_bucket_38  

---

## 🚀 **CRITICAL FIXES IMPLEMENTED**

### **✅ Backend Performance Optimization**
- **Memory:** Increased from 2Gi to **8Gi** (4x improvement)
- **CPU:** Increased from 2 cores to **4 cores** (2x improvement)
- **Concurrency:** Set to 1 for dedicated video processing
- **Max Instances:** Limited to 5 for cost control
- **Result:** Video segmentation should be **significantly faster**

### **✅ Frontend Routing Fix**
- **Issue:** 404 errors when refreshing `/editor` page
- **Fix:** Added SPA routing support in `vercel.json`
- **Result:** Direct access to `/editor` route now works

### **✅ Project State Persistence Fix**
- **Issue:** Node graph losing connections and reverting to linear layout
- **Fix:**
  - Improved node positioning with circular network layout
  - Added automatic edge creation between consecutive scenes
  - Fixed position preservation in compatibility layer
- **Result:** Node graphs maintain network pattern and connections

### **✅ Additional Performance Fixes**
- **Hardcoded URLs:** Fixed all localhost:8000 references in frontend
- **Missing Endpoints:** Added `/api/segment/` and `/api/analysis/` endpoints for timeline sync
- **Video Processing:** Optimized scene detection thresholds for speed
- **Dependencies:** Fixed missing moviepy and imageio packages
- **Backend Resources:** Upgraded to 8Gi memory, 4 CPU cores for faster processing
- **Analysis ID Persistence:** Fixed analysis ID restoration after page refresh
- **Scene Detection:** Fixed VideoManager downscale_factor compatibility issue

---

## 🔄 **Update Workflows**

### **1. Frontend Code Changes**

```bash
# Navigate to project root
cd /home/<USER>/Desktop/insomnia/insomniav23

# Build with production settings
rm -rf dist
VITE_API_URL="https://insomnia-backend-************.asia-southeast1.run.app" \
VITE_ENVIRONMENT="production" \
npm run build

# Deploy to Vercel
vercel --prod --yes

# Update backend CORS for new Vercel URL (if URL changes)
NEW_VERCEL_URL="<new-vercel-url-from-deploy-output>"
gcloud run services update insomnia-backend \
  --region asia-southeast1 \
  --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=insomnia_bucket_38,GCP_PROJECT_ID=cryptic-lattice-463111-k6,FRONTEND_URL=$NEW_VERCEL_URL"
```

### **2. Backend Code Changes**

```bash
# Navigate to backend directory
cd /home/<USER>/Desktop/insomnia/insomniav23/backend

# Ensure video_analysis directory is copied (if modified)
cp -r ../video_analysis ./

# Build new Docker image
gcloud builds submit --tag gcr.io/cryptic-lattice-463111-k6/insomnia-backend

# Deploy updated backend
gcloud run deploy insomnia-backend \
  --image gcr.io/cryptic-lattice-463111-k6/insomnia-backend \
  --platform managed \
  --region asia-southeast1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 8Gi \
  --cpu 4 \
  --timeout 3600 \
  --max-instances 5 \
  --concurrency 1 \
  --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=insomnia_bucket_38,GCP_PROJECT_ID=cryptic-lattice-463111-k6,FRONTEND_URL=https://insomniav23-i5qjidqjs-ps-projects-5d292791.vercel.app"
```

### **3. Video Analysis Scripts Changes**

```bash
# Navigate to project root
cd /home/<USER>/Desktop/insomnia/insomniav23

# Copy updated video_analysis to backend
cp -r video_analysis backend/

# Follow backend deployment steps above
cd backend
gcloud builds submit --tag gcr.io/cryptic-lattice-463111-k6/insomnia-backend
# ... rest of backend deployment
```

### **4. Requirements/Dependencies Changes**

```bash
# Edit backend/requirements.txt
# Then rebuild and redeploy backend:

cd /home/<USER>/Desktop/insomnia/insomniav23/backend
gcloud builds submit --tag gcr.io/cryptic-lattice-463111-k6/insomnia-backend
gcloud run deploy insomnia-backend \
  --image gcr.io/cryptic-lattice-463111-k6/insomnia-backend \
  --platform managed \
  --region asia-southeast1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --max-instances 10 \
  --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=insomnia_bucket_38,GCP_PROJECT_ID=cryptic-lattice-463111-k6,FRONTEND_URL=https://insomniav23-9rsm7ri35-ps-projects-5d292791.vercel.app"

# For frontend dependencies:
cd /home/<USER>/Desktop/insomnia/insomniav23
npm install
# Then follow frontend deployment steps
```

---

## 🧪 **Testing & Debugging**

### **Health Checks**
```bash
# Backend health
curl https://insomnia-backend-************.asia-southeast1.run.app/api/health

# Frontend (should load without errors)
curl -I https://insomniav23-qr8hc6fu8-ps-projects-5d292791.vercel.app
```

### **View Logs**
```bash
# Backend logs
gcloud run services logs read insomnia-backend --region=asia-southeast1 --limit=20

# Real-time logs
gcloud run services logs tail insomnia-backend --region=asia-southeast1
```

### **Test Video Upload**
```bash
# Test with curl (replace with actual video file)
curl -X POST \
  https://insomnia-backend-************.asia-southeast1.run.app/api/analyze \
  -F "video=@test-video.mp4" \
  -F "segmentation_method=cut-based"
```

---

## 🔧 **Environment Variables**

### **Backend Environment Variables**
- `CLOUD_STORAGE_ENABLED=true`
- `GCS_BUCKET_NAME=insomnia_bucket_38`
- `GCP_PROJECT_ID=cryptic-lattice-463111-k6`
- `FRONTEND_URL=<current-vercel-url>`

### **Frontend Build Variables**
- `VITE_API_URL=https://insomnia-backend-************.asia-southeast1.run.app`
- `VITE_ENVIRONMENT=production`

---

## 🚨 **Common Issues & Solutions**

### **CORS Errors**
```bash
# Update backend with new frontend URL
gcloud run services update insomnia-backend \
  --region asia-southeast1 \
  --set-env-vars="FRONTEND_URL=<new-frontend-url>"
```

### **Module Not Found Errors**
```bash
# Check and update requirements.txt
# Rebuild backend with updated dependencies
```

### **Video Analysis Errors**
```bash
# Ensure video_analysis directory is copied to backend
cp -r video_analysis backend/
# Rebuild backend
```

### **Build Failures**
```bash
# Check Docker build logs
gcloud builds log <build-id>

# Check Cloud Run logs
gcloud run services logs read insomnia-backend --region=asia-southeast1
```

---

## 📁 **Project Structure**
```
insomniav23/
├── src/                    # Frontend source
├── backend/               # Backend API
│   ├── video_analysis/   # Video processing scripts (copied)
│   ├── requirements.txt  # Python dependencies
│   └── Dockerfile       # Container config
├── video_analysis/       # Original video scripts
└── dist/                # Frontend build output
```

---

## 🎯 **Quick Deploy Everything**
```bash
cd /home/<USER>/Desktop/insomnia/insomniav23

# 1. Update backend
cd backend
cp -r ../video_analysis ./
gcloud builds submit --tag gcr.io/cryptic-lattice-463111-k6/insomnia-backend
gcloud run deploy insomnia-backend --image gcr.io/cryptic-lattice-463111-k6/insomnia-backend --platform managed --region asia-southeast1 --allow-unauthenticated --port 8080 --memory 2Gi --cpu 2 --timeout 3600 --max-instances 10 --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=insomnia_bucket_38,GCP_PROJECT_ID=cryptic-lattice-463111-k6,FRONTEND_URL=https://insomniav23-9rsm7ri35-ps-projects-5d292791.vercel.app"

# 2. Update frontend
cd ..
rm -rf dist
VITE_API_URL="https://insomnia-backend-************.asia-southeast1.run.app" VITE_ENVIRONMENT="production" npm run build
vercel --prod --yes
```
