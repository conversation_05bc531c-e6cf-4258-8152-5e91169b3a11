# Backend Environment Configuration
# Copy this file to .env and fill in your values

# Cloud Storage Configuration
CLOUD_STORAGE_ENABLED=true
GCS_BUCKET_NAME=insomnia_bucket_38
GCP_PROJECT_ID=cryptic-lattice-463111-k6

# API Configuration
PORT=8080
FRONTEND_URL=https://insomniav23.vercel.app

# Google Cloud Authentication
# Set this to the path of your service account key file
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# AI Processing Configuration
GEMINI_API_KEY="AIzaSyDa9_li6DHS5OqYxXhZ7_JI8R9HNE9mVsQ""

# Development Settings
DEBUG=false
LOG_LEVEL=INFO

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:5173,https://insomniav23.vercel.app
