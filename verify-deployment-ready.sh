#!/bin/bash

# Comprehensive verification script to ensure deployment readiness
set -e

echo "🔍 Verifying Deployment Readiness"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ERRORS=0

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        ERRORS=$((ERRORS + 1))
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

echo "1️⃣ Checking Required Files..."

# Check if all required files exist
files=(
    "backend/Dockerfile"
    "backend/requirements.txt"
    "backend/requirements-minimal.txt"
    "backend/main.py"
    "deploy-hybrid.sh"
    "deploy-gcp-only.sh"
    "vercel.json"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo ""
echo "2️⃣ Checking File Permissions..."

# Check if scripts are executable
scripts=(
    "deploy-hybrid.sh"
    "deploy-gcp-only.sh"
    "test-deployment.sh"
    "test-docker-build.sh"
)

for script in "${scripts[@]}"; do
    if [ -x "$script" ]; then
        print_status 0 "$script is executable"
    else
        print_status 1 "$script is not executable"
        echo "   Fix with: chmod +x $script"
    fi
done

echo ""
echo "3️⃣ Checking Requirements Files..."

# Check requirements.txt syntax
cd backend
if python3 -c "
import pkg_resources
try:
    with open('requirements.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                pkg_resources.Requirement.parse(line)
    print('requirements.txt syntax is valid')
except Exception as e:
    print(f'requirements.txt syntax error: {e}')
    exit(1)
" 2>/dev/null; then
    print_status 0 "requirements.txt syntax valid"
else
    print_status 1 "requirements.txt syntax invalid"
fi

# Check minimal requirements
if python3 -c "
import pkg_resources
try:
    with open('requirements-minimal.txt', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                pkg_resources.Requirement.parse(line)
    print('requirements-minimal.txt syntax is valid')
except Exception as e:
    print(f'requirements-minimal.txt syntax error: {e}')
    exit(1)
" 2>/dev/null; then
    print_status 0 "requirements-minimal.txt syntax valid"
else
    print_status 1 "requirements-minimal.txt syntax invalid"
fi

cd ..

echo ""
echo "4️⃣ Checking Docker Configuration..."

# Check if Dockerfile exists and has basic structure
if grep -q "FROM python" backend/Dockerfile && \
   grep -q "COPY requirements" backend/Dockerfile && \
   grep -q "pip install" backend/Dockerfile; then
    print_status 0 "Dockerfile structure valid"
else
    print_status 1 "Dockerfile missing essential components"
fi

echo ""
echo "5️⃣ Checking Python Code..."

# Check main.py syntax
cd backend
if python3 -m py_compile main.py 2>/dev/null; then
    print_status 0 "main.py syntax valid"
else
    print_status 1 "main.py has syntax errors"
fi

# Check for port consistency
DOCKERFILE_PORT=$(grep "EXPOSE" Dockerfile | grep -o '[0-9]\+' || echo "8080")
MAIN_PY_PORT=$(grep -o 'PORT.*[0-9]\+' main.py | grep -o '[0-9]\+' | tail -1 || echo "8080")

if [ "$DOCKERFILE_PORT" = "$MAIN_PY_PORT" ]; then
    print_status 0 "Port configuration consistent ($DOCKERFILE_PORT)"
else
    print_status 1 "Port mismatch: Dockerfile=$DOCKERFILE_PORT, main.py=$MAIN_PY_PORT"
fi

cd ..

echo ""
echo "6️⃣ Checking Environment Configuration..."

# Check if environment variables are properly referenced
if grep -q "CLOUD_STORAGE_ENABLED" backend/main.py; then
    print_status 0 "Cloud storage environment variables referenced"
else
    print_status 1 "Missing cloud storage environment variables"
fi

if grep -q "VITE_API_URL" vercel.json; then
    print_status 0 "Frontend environment variables configured"
else
    print_status 1 "Missing frontend environment variables"
fi

echo ""
echo "7️⃣ Checking Google Cloud Configuration..."

# Check if gcloud is installed
if command -v gcloud &> /dev/null; then
    print_status 0 "gcloud CLI installed"
    
    # Check if authenticated
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        print_status 0 "gcloud authenticated"
    else
        print_status 1 "gcloud not authenticated (run: gcloud auth login)"
    fi
    
    # Check if project is set
    if gcloud config get-value project &> /dev/null; then
        PROJECT_ID=$(gcloud config get-value project)
        print_status 0 "gcloud project set ($PROJECT_ID)"
    else
        print_status 1 "gcloud project not set (run: gcloud config set project PROJECT_ID)"
    fi
else
    print_status 1 "gcloud CLI not installed"
fi

echo ""
echo "8️⃣ Checking Docker..."

if command -v docker &> /dev/null; then
    print_status 0 "Docker installed"
    
    if docker info &> /dev/null; then
        print_status 0 "Docker daemon running"
    else
        print_status 1 "Docker daemon not running"
    fi
else
    print_status 1 "Docker not installed"
fi

echo ""
echo "📊 Summary"
echo "=========="

if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Ready for deployment.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Set environment variables:"
    echo "   export GCP_PROJECT_ID=\"your-project-id\""
    echo "   export GCS_BUCKET_NAME=\"your-bucket-name\""
    echo ""
    echo "2. Choose deployment strategy:"
    echo "   ./deploy-hybrid.sh     (Vercel + GCP)"
    echo "   ./deploy-gcp-only.sh   (GCP only)"
    echo ""
    echo "3. Test deployment:"
    echo "   ./test-deployment.sh --backend-url https://your-service-url"
else
    echo -e "${RED}❌ Found $ERRORS issues that need to be fixed before deployment.${NC}"
    echo ""
    echo "Please fix the issues above and run this script again."
fi

exit $ERRORS
