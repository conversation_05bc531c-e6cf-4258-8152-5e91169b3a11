#!/bin/bash

# FIXED Deployment script for Hybrid Strategy (Vercel + GCP)
# This script deploys the backend to Google Cloud Run and frontend to Vercel

set -e

echo "🚀 Starting FIXED Hybrid Deployment (Vercel + GCP)"
echo "=================================================="

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-"insomnia-video-editor"}
REGION=${GCP_REGION:-"asia-southeast1"}  # Valid Asia region for Cloud Run
BUCKET_NAME=${GCS_BUCKET_NAME:-"insomnia-video-storage"}
SERVICE_NAME="insomnia-backend"

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI not found. Please install Google Cloud SDK."
    exit 1
fi

# Authenticate with Google Cloud
echo "🔐 Authenticating with Google Cloud..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "Please authenticate with Google Cloud:"
    gcloud auth login --brief
fi

gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable storage.googleapis.com

# Create storage bucket if it doesn't exist
echo "🪣 Setting up Google Cloud Storage..."
if ! gsutil ls -b gs://$BUCKET_NAME &> /dev/null; then
    echo "Creating bucket: $BUCKET_NAME"
    gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME
    
    # Set bucket permissions for public read
    gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME
    
    # Enable CORS for the bucket
    echo '[{"origin":["*"],"method":["GET","POST","PUT","DELETE"],"responseHeader":["Content-Type","Access-Control-Allow-Origin"],"maxAgeSeconds":3600}]' > cors.json
    gsutil cors set cors.json gs://$BUCKET_NAME
    rm cors.json
else
    echo "Bucket $BUCKET_NAME already exists"
fi

# Deploy backend to Google Cloud Run
echo "☁️ Deploying backend to Google Cloud Run..."
cd backend

# Build and deploy using Cloud Build - FIXED VERSION
echo "🏗️ Building with Cloud Build..."
if gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME; then
    echo "✅ Build successful"
else
    echo "❌ Build failed. Trying with minimal requirements..."
    
    # Backup original and use minimal requirements
    cp requirements.txt requirements.txt.backup
    cp requirements-minimal.txt requirements.txt
    
    if gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME; then
        echo "✅ Build successful with minimal requirements"
        # Restore original requirements for future builds
        mv requirements.txt.backup requirements.txt
    else
        echo "❌ Build failed even with minimal requirements"
        # Restore original requirements
        mv requirements.txt.backup requirements.txt
        exit 1
    fi
fi

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8080 \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --max-instances 10 \
    --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=$BUCKET_NAME,GCP_PROJECT_ID=$PROJECT_ID"

# Get the backend URL
BACKEND_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo "✅ Backend deployed to: $BACKEND_URL"

cd ..

# Update vercel.json with the actual backend URL
echo "🔧 Updating Vercel configuration..."
# Create a backup
cp vercel.json vercel.json.backup

# Update the backend URL in vercel.json
sed "s|https://your-backend-url.run.app|$BACKEND_URL|g" vercel.json.backup > vercel.json

# Deploy frontend to Vercel
echo "🌐 Deploying frontend to Vercel..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Set environment variables for build
export VITE_API_URL=$BACKEND_URL
export VITE_ENVIRONMENT=production

# Build and deploy to Vercel
echo "Building and deploying to Vercel..."
vercel --prod --yes

# Test the deployment
echo "🧪 Testing deployment..."
sleep 10  # Wait for services to be ready

if curl -f "$BACKEND_URL/api/health" > /dev/null 2>&1; then
    echo "✅ Backend health check passed"
    
    # Get health details
    HEALTH_RESPONSE=$(curl -s "$BACKEND_URL/api/health")
    echo "📊 Backend status: $HEALTH_RESPONSE"
else
    echo "⚠️ Backend health check failed - service may still be starting"
fi

echo ""
echo "🎉 Hybrid deployment completed successfully!"
echo "============================================="
echo "Backend URL: $BACKEND_URL"
echo "Frontend: Check Vercel dashboard for URL"
echo ""
echo "Next steps:"
echo "1. Test the application functionality"
echo "2. Run: ./test-deployment.sh --backend-url $BACKEND_URL"
echo "3. Set up monitoring and alerts"
echo "4. Configure custom domain (optional)"
echo ""

# Restore original vercel.json
mv vercel.json.backup vercel.json

echo "✅ Deployment complete!"
