{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "rewrites": [{"source": "/api/(.*)", "destination": "https://insomnia-backend-209376606660.asia-southeast1.run.app/api/$1"}, {"source": "/((?!api).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "env": {"VITE_API_URL": "https://insomnia-backend-209376606660.asia-southeast1.run.app", "VITE_ENVIRONMENT": "production"}, "build": {"env": {"VITE_API_URL": "https://insomnia-backend-209376606660.asia-southeast1.run.app", "VITE_ENVIRONMENT": "production"}}}