#!/bin/bash

# Deployment Testing Script
# Tests both hybrid and GCP-only deployments

set -e

echo "🧪 Insomnia Video Editor - Deployment Testing"
echo "============================================="

# Configuration
BACKEND_URL=""
FRONTEND_URL=""
TEST_VIDEO_PATH=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-url)
            BACKEND_URL="$2"
            shift 2
            ;;
        --frontend-url)
            FRONTEND_URL="$2"
            shift 2
            ;;
        --test-video)
            TEST_VIDEO_PATH="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 --backend-url <url> [--frontend-url <url>] [--test-video <path>]"
            echo ""
            echo "Options:"
            echo "  --backend-url   Backend API URL (required)"
            echo "  --frontend-url  Frontend URL (optional, for hybrid deployment)"
            echo "  --test-video    Path to test video file (optional)"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$BACKEND_URL" ]]; then
    echo "❌ Backend URL is required. Use --backend-url <url>"
    exit 1
fi

# Remove trailing slash from URLs
BACKEND_URL=${BACKEND_URL%/}
FRONTEND_URL=${FRONTEND_URL%/}

echo "🔧 Test Configuration:"
echo "Backend URL: $BACKEND_URL"
if [[ -n "$FRONTEND_URL" ]]; then
    echo "Frontend URL: $FRONTEND_URL"
else
    echo "Frontend URL: Same as backend (GCP-only deployment)"
    FRONTEND_URL=$BACKEND_URL
fi
echo ""

# Test 1: Backend Health Check
echo "1️⃣ Testing Backend Health..."
if curl -f -s "$BACKEND_URL/api/health" > /dev/null; then
    echo "✅ Backend health check passed"
    
    # Get health details
    HEALTH_RESPONSE=$(curl -s "$BACKEND_URL/api/health")
    echo "   Response: $HEALTH_RESPONSE"
else
    echo "❌ Backend health check failed"
    echo "   URL: $BACKEND_URL/api/health"
    exit 1
fi

# Test 2: Frontend Accessibility
echo ""
echo "2️⃣ Testing Frontend Accessibility..."
if curl -f -s "$FRONTEND_URL/" > /dev/null; then
    echo "✅ Frontend is accessible"
else
    echo "❌ Frontend is not accessible"
    echo "   URL: $FRONTEND_URL/"
fi

# Test 3: API Endpoints
echo ""
echo "3️⃣ Testing API Endpoints..."

# Test root endpoint
if curl -f -s "$BACKEND_URL/" > /dev/null; then
    echo "✅ Root endpoint accessible"
else
    echo "❌ Root endpoint failed"
fi

# Test CORS preflight
echo "   Testing CORS..."
CORS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" \
    -X OPTIONS \
    -H "Origin: https://example.com" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type" \
    "$BACKEND_URL/api/health")

if [[ "$CORS_RESPONSE" == "200" ]]; then
    echo "✅ CORS configuration working"
else
    echo "⚠️ CORS might need configuration (status: $CORS_RESPONSE)"
fi

# Test 4: Cloud Storage Integration (if enabled)
echo ""
echo "4️⃣ Testing Cloud Storage Integration..."
STORAGE_RESPONSE=$(curl -s "$BACKEND_URL/api/health" | grep -o '"cloud_storage":[^,]*')
if [[ "$STORAGE_RESPONSE" == *"true"* ]]; then
    echo "✅ Cloud storage is enabled"
    
    # Test signed URL generation (if endpoint exists)
    if curl -f -s -X POST "$BACKEND_URL/storage/signed-url" \
        -H "Content-Type: application/json" \
        -d '{"fileName":"test.mp4","contentType":"video/mp4"}' > /dev/null 2>&1; then
        echo "✅ Signed URL generation working"
    else
        echo "⚠️ Signed URL generation might not be configured"
    fi
else
    echo "ℹ️ Cloud storage is disabled"
fi

# Test 5: Video Upload (if test video provided)
if [[ -n "$TEST_VIDEO_PATH" && -f "$TEST_VIDEO_PATH" ]]; then
    echo ""
    echo "5️⃣ Testing Video Upload..."
    
    # Check file size
    FILE_SIZE=$(stat -f%z "$TEST_VIDEO_PATH" 2>/dev/null || stat -c%s "$TEST_VIDEO_PATH" 2>/dev/null || echo "0")
    echo "   Test video size: $(($FILE_SIZE / 1024 / 1024)) MB"
    
    if [[ $FILE_SIZE -gt 104857600 ]]; then  # 100MB
        echo "⚠️ Test video is large (>100MB), upload test may take time"
    fi
    
    # Attempt upload
    echo "   Uploading test video..."
    UPLOAD_RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$BACKEND_URL/api/analyze" \
        -F "video=@$TEST_VIDEO_PATH" \
        -F "segmentation_method=cut-based" \
        --max-time 120)
    
    HTTP_CODE=$(echo "$UPLOAD_RESPONSE" | tail -n1)
    RESPONSE_BODY=$(echo "$UPLOAD_RESPONSE" | head -n -1)
    
    if [[ "$HTTP_CODE" == "200" ]]; then
        echo "✅ Video upload and analysis successful"
        
        # Extract analysis ID if present
        ANALYSIS_ID=$(echo "$RESPONSE_BODY" | grep -o '"analysisId":"[^"]*' | cut -d'"' -f4)
        if [[ -n "$ANALYSIS_ID" ]]; then
            echo "   Analysis ID: $ANALYSIS_ID"
            
            # Test analysis retrieval
            if curl -f -s "$BACKEND_URL/api/analysis/$ANALYSIS_ID" > /dev/null; then
                echo "✅ Analysis data retrieval working"
            else
                echo "⚠️ Analysis data retrieval failed"
            fi
        fi
    else
        echo "❌ Video upload failed (HTTP $HTTP_CODE)"
        echo "   Response: $RESPONSE_BODY"
    fi
else
    echo ""
    echo "5️⃣ Skipping Video Upload Test (no test video provided)"
    echo "   To test video upload, use: --test-video /path/to/video.mp4"
fi

# Test 6: Performance Check
echo ""
echo "6️⃣ Performance Check..."

# Measure response time
START_TIME=$(date +%s%N)
curl -f -s "$BACKEND_URL/api/health" > /dev/null
END_TIME=$(date +%s%N)
RESPONSE_TIME=$(( (END_TIME - START_TIME) / 1000000 ))

echo "   API response time: ${RESPONSE_TIME}ms"

if [[ $RESPONSE_TIME -lt 1000 ]]; then
    echo "✅ Good response time (<1s)"
elif [[ $RESPONSE_TIME -lt 3000 ]]; then
    echo "⚠️ Acceptable response time (1-3s)"
else
    echo "❌ Slow response time (>3s)"
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="
echo "Backend URL: $BACKEND_URL"
echo "Frontend URL: $FRONTEND_URL"
echo "Response Time: ${RESPONSE_TIME}ms"

# Final recommendations
echo ""
echo "💡 Recommendations:"
echo "- Monitor response times in production"
echo "- Set up proper logging and alerting"
echo "- Test with various video formats and sizes"
echo "- Implement proper error handling for large uploads"
echo "- Consider implementing health check endpoints for monitoring"

echo ""
echo "🎉 Deployment testing completed!"
