// Quick test to verify environment configuration
console.log('Testing environment configuration...');

// Test the environment detection
import('./src/config/environment.js').then(env => {
  console.log('✅ Environment module loaded successfully');
  console.log('Current environment:', env.getCurrentEnvironment());
  console.log('API base URL:', env.getApiBaseUrl());
  
  // Test API health check
  env.checkApiHealth().then(isHealthy => {
    console.log('API health check result:', isHealthy ? '✅ Healthy' : '❌ Unhealthy');
  }).catch(error => {
    console.error('API health check error:', error);
  });
}).catch(error => {
  console.error('❌ Failed to load environment module:', error);
});
