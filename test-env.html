<!DOCTYPE html>
<html>
<head>
    <title>Environment Test</title>
</head>
<body>
    <h1>Environment Configuration Test</h1>
    <div id="results"></div>
    
    <script type="module">
        const results = document.getElementById('results');
        
        function log(message, isError = false) {
            const div = document.createElement('div');
            div.textContent = message;
            div.style.color = isError ? 'red' : 'green';
            div.style.margin = '5px 0';
            results.appendChild(div);
        }
        
        try {
            // Test import.meta.env
            log('✅ import.meta.env is available');
            log(`NODE_ENV: ${import.meta.env.NODE_ENV || 'undefined'}`);
            log(`VITE_API_URL: ${import.meta.env.VITE_API_URL || 'undefined'}`);
            
            // Test environment detection
            const getCurrentEnvironment = () => {
                if (import.meta.env.NODE_ENV) {
                    return import.meta.env.NODE_ENV;
                }
                
                if (import.meta.env.VERCEL_ENV) {
                    return import.meta.env.VERCEL_ENV === 'production' ? 'production' : 'staging';
                }
                
                if (typeof window !== 'undefined') {
                    const hostname = window.location.hostname;
                    
                    if (hostname === 'localhost' || hostname === '127.0.0.1') {
                        return 'development';
                    }
                    
                    if (hostname.includes('staging') || hostname.includes('dev')) {
                        return 'staging';
                    }
                    
                    return 'production';
                }
                
                return 'development';
            };
            
            const env = getCurrentEnvironment();
            log(`✅ Detected environment: ${env}`);
            
            // Test API URL construction
            const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
            log(`✅ API URL: ${apiUrl}`);
            
            // Test fetch to backend
            fetch(`${apiUrl}/api/health`)
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error(`HTTP ${response.status}`);
                })
                .then(data => {
                    log(`✅ Backend health check: ${data.status}`);
                })
                .catch(error => {
                    log(`❌ Backend health check failed: ${error.message}`, true);
                });
                
        } catch (error) {
            log(`❌ Test failed: ${error.message}`, true);
        }
    </script>
</body>
</html>
