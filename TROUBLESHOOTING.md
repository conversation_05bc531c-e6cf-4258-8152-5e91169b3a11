# 🔧 Troubleshooting Guide - Insomnia Video Editor Deployment

## 🐳 Docker Build Issues

### Problem: `Cannot import 'setuptools.build_meta'`

**Symptoms:**
```
pip._vendor.pyproject_hooks._impl.BackendUnavailable: Cannot import 'setuptools.build_meta'
```

**Solutions:**

1. **Use the fixed requirements.txt** (already implemented):
   ```bash
   # The requirements.txt now includes build dependencies at the top
   setuptools>=65.0.0
   wheel>=0.38.0
   ```

2. **Test locally first**:
   ```bash
   ./test-docker-build.sh
   ```

3. **Use minimal requirements for testing**:
   ```bash
   cd backend
   docker build -t test --build-arg REQUIREMENTS_FILE=requirements-minimal.txt .
   ```

4. **Use Alpine version** (smaller, more reliable):
   ```bash
   cd backend
   docker build -f Dockerfile.alpine -t test .
   ```

### Problem: Large dependencies causing timeouts

**Symptoms:**
- Build takes too long
- Cloud Build timeout errors
- Memory issues during build

**Solutions:**

1. **Use minimal requirements** for basic functionality:
   ```bash
   # Edit backend/requirements.txt and comment out:
   # torch>=2.0.0
   # openai-whisper>=20231117
   ```

2. **Increase Cloud Build timeout**:
   ```bash
   gcloud builds submit --timeout=1800s --tag gcr.io/PROJECT_ID/SERVICE_NAME
   ```

3. **Use multi-stage build** (already implemented in Dockerfile)

## 🌐 CORS Issues

### Problem: Frontend can't connect to backend

**Symptoms:**
```
Access to fetch at 'https://backend-url' from origin 'https://frontend-url' has been blocked by CORS policy
```

**Solutions:**

1. **Set FRONTEND_URL environment variable**:
   ```bash
   gcloud run services update SERVICE_NAME \
     --set-env-vars="FRONTEND_URL=https://your-frontend-url.vercel.app"
   ```

2. **Update vercel.json** with correct backend URL:
   ```json
   {
     "routes": [
       {
         "src": "/api/(.*)",
         "dest": "https://your-actual-backend-url.run.app/api/$1"
       }
     ]
   }
   ```

## ☁️ Google Cloud Issues

### Problem: Permission denied errors

**Symptoms:**
```
ERROR: (gcloud.run.deploy) User [email] does not have permission to access service [service-name]
```

**Solutions:**

1. **Enable required APIs**:
   ```bash
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable run.googleapis.com
   gcloud services enable containerregistry.googleapis.com
   ```

2. **Set correct project**:
   ```bash
   gcloud config set project YOUR_PROJECT_ID
   gcloud auth login
   ```

3. **Grant necessary permissions**:
   ```bash
   # Add Cloud Run Admin role
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="user:<EMAIL>" \
     --role="roles/run.admin"
   ```

### Problem: Storage bucket access denied

**Symptoms:**
```
403 Forbidden: Access denied to bucket
```

**Solutions:**

1. **Create bucket with correct permissions**:
   ```bash
   gsutil mb gs://your-bucket-name
   gsutil iam ch allUsers:objectViewer gs://your-bucket-name
   ```

2. **Set service account permissions**:
   ```bash
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="serviceAccount:SERVICE_ACCOUNT_EMAIL" \
     --role="roles/storage.admin"
   ```

## 🚀 Deployment Issues

### Problem: Service not responding after deployment

**Symptoms:**
- Health check fails
- 502/503 errors
- Service shows as "not ready"

**Solutions:**

1. **Check logs**:
   ```bash
   gcloud logs tail "resource.type=cloud_run_revision"
   ```

2. **Verify environment variables**:
   ```bash
   gcloud run services describe SERVICE_NAME --region=REGION
   ```

3. **Test with minimal configuration**:
   ```bash
   # Deploy with minimal requirements
   ./deploy-hybrid.sh
   # When prompted, choose to test locally first
   ```

4. **Check port configuration**:
   ```python
   # In main.py, ensure:
   port = int(os.getenv("PORT", 8080))
   uvicorn.run("main:app", host="0.0.0.0", port=port)
   ```

## 🧪 Testing Issues

### Problem: Video upload fails

**Symptoms:**
- Upload returns 500 error
- Processing timeout
- Memory errors

**Solutions:**

1. **Test with small video first**:
   ```bash
   ./test-deployment.sh --backend-url URL --test-video small-video.mp4
   ```

2. **Increase memory allocation**:
   ```bash
   gcloud run services update SERVICE_NAME \
     --memory=4Gi --cpu=2
   ```

3. **Check video format compatibility**:
   ```bash
   # Supported formats: mp4, avi, mov
   # Max size: 1GB (configurable)
   ```

## 🔍 Debug Commands

### Check service status
```bash
gcloud run services list
gcloud run services describe SERVICE_NAME --region=REGION
```

### View logs
```bash
# Real-time logs
gcloud logs tail "resource.type=cloud_run_revision"

# Historical logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50
```

### Test endpoints
```bash
# Health check
curl https://SERVICE_URL/api/health

# Root endpoint
curl https://SERVICE_URL/

# With verbose output
curl -v https://SERVICE_URL/api/health
```

### Local testing
```bash
# Test Docker build
./test-docker-build.sh

# Test deployment
./test-deployment.sh --backend-url https://SERVICE_URL
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the logs** first using the debug commands above
2. **Try the minimal configuration** to isolate the problem
3. **Test locally** before deploying to cloud
4. **Review the environment variables** and configuration

### Common Environment Variables to Check
```bash
# Backend
CLOUD_STORAGE_ENABLED=true
GCS_BUCKET_NAME=your-bucket-name
GCP_PROJECT_ID=your-project-id
PORT=8080

# Frontend
VITE_API_URL=https://your-backend-url
VITE_ENVIRONMENT=production
```

### Quick Recovery Steps
```bash
# If everything fails, start fresh:
1. Delete the service: gcloud run services delete SERVICE_NAME
2. Use minimal requirements: cp requirements-minimal.txt requirements.txt
3. Deploy again: ./deploy-hybrid.sh
4. Test: ./test-deployment.sh --backend-url NEW_URL
```
