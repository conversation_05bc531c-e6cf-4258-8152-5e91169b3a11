# 🚀 Insomnia Video Editor - Complete Deployment Guide

This guide provides step-by-step instructions for deploying the Insomnia video editing application using two different strategies.

## 📋 Prerequisites

### Required Tools
- **Node.js** (v18 or higher)
- **Google Cloud SDK** (`gcloud` CLI)
- **Docker** (for GCP-only deployment)
- **Vercel CLI** (for hybrid deployment): `npm i -g vercel`

### Required Accounts
- **Google Cloud Platform** account with billing enabled
- **Vercel** account (for hybrid deployment)
- **GitHub** account (recommended for CI/CD)

## 🎯 Deployment Strategies

### Strategy 1: Hybrid Deployment (Vercel + GCP)
- **Frontend**: Deployed on Vercel (CDN, fast global delivery)
- **Backend**: Deployed on Google Cloud Run (scalable, serverless)
- **Storage**: Google Cloud Storage for large files
- **Best for**: Production applications with global users

### Strategy 2: GCP-Only Deployment
- **Frontend + Backend**: Single container on Google Cloud Run
- **Storage**: Google Cloud Storage
- **Best for**: Enterprise environments, single cloud provider preference

## 🔧 Pre-Deployment Setup

### 1. Environment Configuration

Create environment files:

```bash
# Backend environment
cp backend/.env.example backend/.env
```

Edit `backend/.env` with your values:
```env
CLOUD_STORAGE_ENABLED=true
GCS_BUCKET_NAME=your-unique-bucket-name
GCP_PROJECT_ID=your-gcp-project-id
GEMINI_API_KEY=your-gemini-api-key
```

### 2. Google Cloud Setup

```bash
# Install Google Cloud SDK (if not installed)
# Visit: https://cloud.google.com/sdk/docs/install

# Authenticate
gcloud auth login

# Create a new project (optional)
gcloud projects create your-project-id --name="Insomnia Video Editor"

# Set the project
gcloud config set project your-project-id

# Enable billing (required for Cloud Run and Storage)
# Visit: https://console.cloud.google.com/billing
```

## 🌐 Strategy 1: Hybrid Deployment (Vercel + GCP)

### Step 1: Deploy Backend to GCP

```bash
# Set environment variables
export GCP_PROJECT_ID="your-project-id"
export GCS_BUCKET_NAME="your-unique-bucket-name"

# Run the deployment script
./deploy-hybrid.sh
```

### Step 2: Deploy Frontend to Vercel

The deployment script will automatically:
1. Build and deploy the backend to Cloud Run
2. Update the Vercel configuration with the backend URL
3. Deploy the frontend to Vercel

### Manual Vercel Deployment (Alternative)

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Set environment variables
export VITE_API_URL="https://your-backend-url.run.app"
export VITE_ENVIRONMENT="production"

# Deploy
vercel --prod
```

## ☁️ Strategy 2: GCP-Only Deployment

### Single Command Deployment

```bash
# Set environment variables
export GCP_PROJECT_ID="your-project-id"
export GCS_BUCKET_NAME="your-unique-bucket-name"

# Run the deployment script
./deploy-gcp-only.sh
```

### Manual GCP-Only Deployment

```bash
# Build and submit to Cloud Build
gcloud builds submit --config cloudbuild-fullstack.yaml

# The Cloud Build will automatically deploy to Cloud Run
```

## 🧪 Testing Your Deployment

### Health Checks

```bash
# Test backend health
curl https://your-backend-url/api/health

# Test frontend (should return HTML)
curl https://your-frontend-url/

# Test video upload endpoint
curl -X POST https://your-backend-url/api/analyze \
  -F "video=@test-video.mp4" \
  -F "segmentation_method=cut-based"
```

### Functional Testing

1. **Upload a video file**
2. **Verify AI processing works**
3. **Test video export functionality**
4. **Check cloud storage integration**

## 🔒 Security Configuration

### Environment Variables

Set these in your deployment environment:

```bash
# Google Cloud Run
gcloud run services update your-service-name \
  --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=your-bucket"

# Vercel
vercel env add VITE_API_URL production
```

### CORS Configuration

The backend automatically configures CORS for:
- `localhost:5173` (development)
- `*.vercel.app` (Vercel deployments)
- Custom domains (via `FRONTEND_URL` env var)

## 📊 Monitoring and Logging

### Google Cloud Monitoring

```bash
# View logs
gcloud logs read "resource.type=cloud_run_revision"

# Set up alerts
gcloud alpha monitoring policies create --policy-from-file=monitoring-policy.yaml
```

### Performance Monitoring

- **Cloud Run**: Built-in metrics in GCP Console
- **Vercel**: Analytics dashboard
- **Storage**: GCS usage metrics

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check `FRONTEND_URL` environment variable
   - Verify allowed origins in backend

2. **File Upload Failures**
   - Check Cloud Storage bucket permissions
   - Verify service account has storage.objects.create permission

3. **Memory Issues**
   - Increase Cloud Run memory allocation
   - Optimize video processing parameters

4. **Timeout Errors**
   - Increase Cloud Run timeout (max 3600s)
   - Implement async processing for large files

### Debug Commands

```bash
# Check Cloud Run service status
gcloud run services describe your-service-name --region=us-central1

# View recent logs
gcloud logs tail "resource.type=cloud_run_revision"

# Test storage bucket access
gsutil ls gs://your-bucket-name
```

## 🔄 CI/CD Setup

### GitHub Actions (Recommended)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to GCP
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: google-github-actions/setup-gcloud@v0
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: your-project-id
      - run: ./deploy-gcp-only.sh
```

## 📈 Scaling Considerations

### Performance Optimization

- **Cloud Run**: Auto-scales based on traffic
- **Storage**: Use CDN for frequently accessed files
- **Database**: Consider Cloud SQL for persistent data

### Cost Optimization

- **Cloud Run**: Pay per request, scales to zero
- **Storage**: Lifecycle policies for old files
- **Monitoring**: Set up billing alerts

## 🆘 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review Google Cloud logs
3. Verify all environment variables are set
4. Test with a minimal video file first

---

**Next Steps**: After successful deployment, consider setting up monitoring, custom domains, and automated backups.
