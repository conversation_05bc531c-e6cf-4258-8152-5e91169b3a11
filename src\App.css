* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary: #8a2be2; /* Purple */
  --primary-light: #9d4edd;
  --primary-dark: #6a0dad;
  --accent: #ff7eee; /* Pinkish accent */
  --text-light: #f0f0f0; /* Lighter text for better contrast on dark */
  --text-dark: #121214;
  --bg-dark: #13111b; /* Main background */
  --bg-medium: #201c2b; /* Sidebar panel, node background */
  --bg-light: #2d2b3a; /* Icon bar, controls background */
  --bg-icon-bar: #1a1822; /* Darker, more distinct for icon bar */
  --success: #2ecc71; /* Green for success/add */
  --success-dark: #27ae60;
  --danger: #e74c3c; /* Red for delete/error */
  --danger-dark: #c0392b;
  --warning: #f39c12; /* Orange for warning/snap */
  --info: #3498db; /* Blue for info/intro */
  --shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Softer shadow */
  --shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.3);
  --transition: all 0.25s ease-in-out;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --sidebar-icon-bar-width: 48px; /* Slimmer */
  --sidebar-panel-width: 250px; /* Adjusted from your 200px for a bit more space */
  --warning-rgb: 243, 156, 18; /* Corresponds to #f39c12 */
  --primary-rgb: 138, 43, 226;
}

body, html, #root {
  height: 100%;
  width: 100%;
  background-color: var(--bg-dark);
  color: var(--text-light);
  overflow: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Floating Navigation Layout */
.app-header {
  position: relative;
  height: 60px; /* Reserve space for floating elements */
  flex-shrink: 0;
  pointer-events: none; /* Allow clicks through to content */
}

/* Cool Watermark Title */
.app-title {
  position: absolute;
  top: 16px;
  right: 20px;
  color: transparent;
  font-size: 1.4rem;
  font-weight: 700;
  letter-spacing: 2px;
  text-transform: uppercase;
  background: linear-gradient(45deg,
    rgba(138, 43, 226, 1) 0%,
    rgba(157, 78, 221, 1) 35%,
    rgb(220, 79, 239) 65%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  z-index: 150;
  pointer-events: none;
  user-select: none;
  font-family: 'Inter', sans-serif;
}


/* Slim Minimalistic Navigation */
.view-toggle-container {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1px;
  background-color: rgba(19, 17, 27, 0.7);
  backdrop-filter: blur(8px);
  border-radius: 2px;
  padding: 3px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(138, 43, 226, 0.2);
  z-index: 200;
  pointer-events: auto;
}

/* Slim Minimalistic Buttons with Smooth Animations */
.view-toggle-button {
  background-color: transparent;
  color: rgba(240, 240, 240, 0.7);
  border: none;
  padding: 1px 14px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 70px;
  justify-content: center;
  height: 32px;
  position: relative;
  transform: scale(1);
}

.view-toggle-button:hover {
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(138, 43, 226, 0.1);
  transform: scale(1.02);
}

.view-toggle-button.active {
  background: linear-gradient(135deg,
    rgba(138, 43, 226, 0.8) 0%,
    rgba(157, 78, 221, 0.7) 100%);
  color: white;
  box-shadow: 0 1px 8px rgba(138, 43, 226, 0.3);
  transform: scale(1.05);
}

.view-toggle-button.active:hover {
  background: linear-gradient(135deg,
    rgba(138, 43, 226, 0.9) 0%,
    rgba(157, 78, 221, 0.8) 100%);
  box-shadow: 0 2px 12px rgba(138, 43, 226, 0.4);
  transform: scale(1.05);
}

.view-toggle-button:focus {
  outline: none;
}

.main-content-wrapper {
  display: flex;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

.sidebar-container {
  display: flex;
  height: 100%;
  position: relative;
  z-index: 100;
  box-shadow: var(--shadow);
}

.sidebar-icon-bar {
  width: var(--sidebar-icon-bar-width);
  background-color: var(--bg-icon-bar);
  padding: 0.5rem 0;
  /* background-color: rgba(18, 17, 17, 0.85); Redundant, var(--bg-icon-bar) used */
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1px solid #2a2835;
  flex-shrink: 0;
  justify-content: space-between;
}

.sidebar-icon-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
}

.sidebar-icon {
  background-color: transparent;
  color: var(--text-light);
  border: none;
  padding: 0;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 1.3rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease, color 0.2s ease, opacity 0.2s ease;
  opacity: 0.6;
  margin: 0.25rem 0;
}
.sidebar-icon.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.sidebar-icon.disabled:hover {
  background-color: transparent;
}
.sidebar-icon:hover:not(.disabled) {
  background-color: var(--bg-light);
  opacity: 1;
}
.sidebar-icon.active {
  background-color: var(--primary);
  color: white;
  opacity: 1;
}
.sidebar-icon.active:hover {
  background-color: var(--primary-light);
}
.story-node.pending-changes-node {
  outline: 2px dashed var(--warning) !important; /* Or some other visual cue */
  outline-offset: 2px;
}

.sidebar-panel {
  width: 0;
  background-color: var(--bg-medium); /* Maintained from your version */
  overflow: hidden;
  transition: width 0.25s ease-in-out, padding 0.25s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  border-left: 1px solid var(--bg-icon-bar);
}
.sidebar-panel.open {
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
  width: var(--sidebar-panel-width);
  padding: 0.75rem 1rem;
  overflow-y: auto;
  background-color: rgba(18, 17, 17, 0.85);  /* Kept your specific rgba */
  border-left: 1px solid var(--primary-dark);
  border-right: 1px solid rgba(255, 255, 255, 0.12); /* Kept your specific border */
}
.sidebar-panel.open::-webkit-scrollbar { width: 6px; }
.sidebar-panel.open::-webkit-scrollbar-thumb { background-color: var(--primary-dark); border-radius: 3px; }
.sidebar-panel.open::-webkit-scrollbar-track { background-color: var(--bg-light); }
.sidebar-expand-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--bg-icon-bar);
  color: var(--text-light);
  border: 1px solid var(--primary-dark);
  border-left: none;
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
  padding: 10px 8px;
  cursor: pointer;
  z-index: 150;
  font-size: 1.2rem;
  line-height: 1;
  box-shadow: var(--shadow);
  transition: var(--transition);
}
.sidebar-expand-button:hover {
  background-color: var(--primary);
  color: white;
}

.sidebar-icon-bar .sidebar-icon-group:last-child {
    margin-top: auto; /* Pushes settings and collapse to the bottom */
}
.sidebar-collapse-control-button {
  margin-top: 0.5rem; /* Space above collapse button */
}


/* Metadata Editor Styles */
.metadata-editor-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}
.metadata-editor-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap; /* Allow wrapping for smaller screens */
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--primary-dark);
}
.bulk-tag-section {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
.bulk-tag-input {
  padding: 0.4rem 0.6rem;
  font-size: 0.75rem;
  background-color: var(--bg-light);
  color: var(--text-light);
  border: 1px solid var(--primary-dark);
  border-radius: var(--border-radius-sm);
  width: 120px; /* Adjust as needed */
}
.bulk-tag-input:focus {
  outline: none;
  border-color: var(--accent);
}
.bulk-tag-apply, .save-all-button {
  padding: 0.4rem 0.8rem; /* Smaller padding for these buttons */
  font-size: 0.75rem;
}
.save-all-button:disabled {
  background-color: var(--bg-medium);
  opacity: 0.6;
  cursor: not-allowed;
}

.metadata-editor-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
  table-layout: fixed; /* Helps with column widths */
}
.metadata-editor-table th,
.metadata-editor-table td {
  border-bottom: 1px solid var(--bg-light);
  padding: 0.5rem 0.4rem;
  text-align: left;
  vertical-align: middle;
}
.metadata-editor-table th {
  color: var(--primary-light);
  font-size: 0.7rem;
  text-transform: uppercase;
  font-weight: 500;
}
.metadata-editor-table th:first-child, /* Sel column */
.metadata-editor-table td:first-child {
  width: 30px;
  text-align: center;
}
.metadata-editor-table th:nth-child(2), /* # column */
.metadata-editor-table td:nth-child(2) {
  width: 30px;
  text-align: right;
  padding-right: 0.5rem;
}
.metadata-editor-table th:nth-child(3) { width: auto; } /* Title */
.metadata-editor-table th:nth-child(4) { width: 40%; } /* Tags */


.metadata-editor-row.selected {
  background-color: rgba(var(--primary-rgb, 138, 43, 226), 0.15);
}
.metadata-editor-row.pending-save {
  /* background-color: rgba(var(--warning-rgb, 243, 156, 18), 0.1); /* Using warning color */
  outline: 1px dashed var(--warning);
  outline-offset: -1px;
}
.metadata-editor-row.pending-save.selected {
  background-color: rgba(var(--primary-rgb, 138, 43, 226), 0.25); /* Darker if also selected */
}


.metadata-editor-table input[type="text"] {
  width: 100%;
  padding: 0.3rem 0.5rem;
  font-size: 0.75rem;
  background-color: var(--bg-light);
  color: var(--text-light);
  border: 1px solid transparent; /* No border by default */
  border-radius: var(--border-radius-sm);
  box-sizing: border-box;
}
.metadata-editor-table input[type="text"]:focus {
  outline: none;
  border-color: var(--accent);
  background-color: var(--bg-medium);
}
.metadata-editor-table input[type="text"].invalid {
  border-color: var(--danger) !important;
}

.metadata-editor-table .row-checkbox {
  accent-color: var(--primary);
  width: 14px;
  height: 14px;
}

.sidebar-panel-content { display: flex; flex-direction: column; gap: 1rem; }
.panel-section { display: flex; flex-direction: column; gap: 0.6rem; }
.panel-section h4 {
  margin-bottom: 0.4rem; color: var(--primary-light); font-size: 0.9rem; font-weight: 500;
  border-bottom: 1px solid var(--primary-dark); padding-bottom: 0.4rem;
  text-transform: uppercase; letter-spacing: 0.5px;
}
.panel-placeholder-text {
  font-size: 0.8rem; color: var(--text-light); opacity: 0.6; text-align: center; padding: 1rem 0.5rem;
}

.control-button, .nav-button {
  background-color: var(--bg-light); color: var(--text-light); border: 1px solid var(--bg-light);
  padding: 0.45rem 0.75rem; border-radius: var(--border-radius-sm); cursor: pointer;
  font-size: 0.8rem; text-align: left; transition: var(--transition); box-shadow: none;
  width: 100%; display: flex; align-items: center; gap: 0.5rem;
}
.nav-button { justify-content: center; }
.control-button:hover, .nav-button:hover {
  background-color: var(--primary-dark); border-color: var(--primary-light);
  transform: translateY(0px); color: white;
}
.control-button.delete { background-color: var(--danger); border-color: var(--danger); }
.control-button.delete:hover { background-color: var(--danger-dark); border-color: var(--danger-dark); }

.control-label {
  font-size: 0.75rem; display: flex; align-items: center; gap: 0.4rem;
  padding: 0.3rem 0; cursor: pointer; color: var(--text-light); opacity: 0.9;
}
.control-label input[type="checkbox"] { accent-color: var(--primary); width: 13px; height: 13px; }

.video-uploader-container { padding: 0; margin-top: 0; border-top: none; }
.video-uploader-container .drop-zone { padding: 0.8rem; min-height: 50px; border-radius: var(--border-radius-sm); }
.video-uploader-container .drop-zone p { font-size: 0.8rem; }

/* Segmentation Method Selector */
.segmentation-method-selector {
  margin: 0.75rem 0;
  padding: 0.5rem;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.segmentation-method-label {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease;
}

.segmentation-method-label:hover {
  background-color: var(--bg-hover);
}

.segmentation-method-label:last-child {
  margin-bottom: 0;
}

.segmentation-method-label input[type="radio"] {
  margin: 0;
  accent-color: var(--primary);
}

.segmentation-method-label .method-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.85rem;
}

.segmentation-method-label .method-description {
  color: var(--text-secondary);
  font-size: 0.75rem;
  margin-left: auto;
  text-align: right;
  flex-shrink: 0;
}

.segmentation-method-label input[type="radio"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.segmentation-method-label:has(input[type="radio"]:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

/* === Layout CSS for Canvas with Smooth Transitions === */
.content-area-with-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0.5rem;
  position: relative;
}

.content {
  background-color: var(--bg-dark);
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Smooth Content Transitions */
.story-web-canvas {
  flex-grow: 1;
  border: none;
  background-color: var(--bg-dark);
  width: 100%;
  height: 100%;
  position: relative;
  outline: none;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.canvas-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-light);
  opacity: 0.5;
  text-align: center;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.canvas-placeholder p {
  margin-bottom: 0.5rem;
}

/* Timeline Placeholder Container */
.timeline-placeholder-container {
  flex-grow: 1;
  background-color: var(--bg-dark);
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-placeholder {
  text-align: center;
  color: var(--text-light);
  opacity: 0.7;
}

.timeline-placeholder p {
  margin-bottom: 0.5rem;
}

/* View Transition Animation Classes */
.view-enter {
  opacity: 0;
  transform: translateY(20px);
}

.view-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-exit {
  opacity: 1;
  transform: translateY(0);
}

.view-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth View Transition Keyframes */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* === End Layout CSS === */

/* Custom Node Styling */
.story-node {
  background-color: var(--bg-medium); border: 1px solid var(--primary); border-radius: var(--border-radius-md);
  padding: 10px 15px; min-width: 150px; min-height: 60px; text-align: center;
  box-shadow: var(--shadow); cursor: grab; user-select: none; z-index: 1;
  transition: var(--transition); color: var(--text-light); position: relative;
  display: flex; flex-direction: column; justify-content: center; align-items: center;
}
.story-node:active { cursor: grabbing; }
.story-node:hover { box-shadow: var(--shadow-hover); border-color: var(--primary-light); transform: translateY(-2px) scale(1.02); }
.story-node.selected { border-color: var(--accent); box-shadow: 0 0 0 2px var(--accent), var(--shadow-hover); transform: scale(1.03); }
.story-node.type-entry { border-left: 5px solid var(--success); }
.story-node.type-scene { border-left: 5px solid var(--primary); min-width: 250px; min-height: 100px; max-width: 400; max-height: 500; }
.story-node.type-note { border-left: 5px solid var(--warning); }
.story-node.type-intro { border-left: 5px solid var(--info); }
.story-node.type-outro { border-left: 5px solid var(--danger); }

/* React Flow Elements */
.react-flow__edge-path { stroke: var(--primary-light); stroke-width: 2.5px; filter: drop-shadow(0px 1px 1px rgba(0,0,0,0.2)); }
.react-flow__edge.react-flow__edge-type-smoothstep .react-flow__edge-path,
.react-flow__edge.react-flow__edge-type-default .react-flow__edge-path,
.react-flow__edge.react-flow__edge-type-custom .react-flow__edge-path { stroke: url(#edge-gradient); }
.react-flow__edge.selected .react-flow__edge-path,
.react-flow__edge:hover .react-flow__edge-path { stroke: var(--accent); stroke-width: 3.5px; }
.react-flow__edges svg defs + #edge-gradient {}
.react-flow__handle {
  width: 10px; height: 10px; background-color: var(--primary); border: 2px solid var(--bg-medium);
  border-radius: 50%; transition: var(--transition);
}
.react-flow__handle:hover, .react-flow__handle-connecting { background-color: var(--accent); transform: scale(1.2); border-color: var(--accent); }
.custom-handle {}
.react-flow__controls { box-shadow: var(--shadow); border-radius: var(--border-radius-md); overflow: hidden; background-color: var(--bg-light); }
.react-flow__controls-button {
  background-color: var(--bg-light); color: var(--text-light); border: none;
  border-bottom: 1px solid var(--primary-dark); width: 30px; height: 30px;
}
.react-flow__controls-button:hover { background-color: var(--bg-medium); color: var(--accent); }
.react-flow__controls-button:last-child { border-bottom: none; }
.react-flow__minimap { background-color: var(--bg-medium); border-radius: var(--border-radius-sm); box-shadow: var(--shadow); }
.react-flow__minimap-mask { fill: rgba(var(--primary-rgb, 138, 43, 226), 0.15); }
.react-flow__pane { cursor: default; }
.react-flow__node { background: transparent !important; border: none !important; padding: 0 !important; border-radius: 0 !important; }

/* Node Internals (Badges, Buttons, Title, Tags, Video Info) */
.node-type-badge {
  position: absolute; top: -10px; left: 50%; transform: translateX(-50%); font-size: 0.65rem;
  padding: 3px 8px; border-radius: 12px; text-transform: uppercase; font-weight: 600;
  box-shadow: var(--shadow); z-index: 2;
}
.node-type-badge.scene { background-color: var(--primary); color: white; }
.node-type-badge.note { background-color: var(--warning); color: var(--bg-dark); }
.node-type-badge.entry { background-color: var(--success); color: white; }
.node-type-badge.intro { background-color: var(--info); color: white; }
.node-type-badge.outro { background-color: var(--danger); color: white; }

.node-delete-button, .node-connect-button {
  position: absolute; background-color: rgba(255, 255, 255, 0.1); border: none; font-size: 1rem;
  cursor: pointer; opacity: 0.7; transition: var(--transition); padding: 4px;
  border-radius: var(--border-radius-sm); z-index: 10; line-height: 1;
}
.node-delete-button:hover, .node-connect-button:hover { opacity: 1; transform: scale(1.1); }
.node-delete-button { top: 5px; right: 5px; color: var(--danger); }
.node-delete-button:hover { background-color: rgba(231, 76, 60, 0.3); }
.node-connect-button { bottom: 5px; right: 5px; color: var(--success); }
.node-connect-button:hover { background-color: rgba(46, 204, 113, 0.3); }

.node-title {
  margin: 0; padding: 5px 0; font-size: 0.95rem; font-weight: 600; cursor: text;
  width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
}
.node-title-input-inline {
  background-color: var(--bg-light); color: var(--text-light); border: 1px solid var(--primary-light);
  border-radius: var(--border-radius-sm); padding: 4px 6px; font-size: 0.95rem;
  width: calc(100% - 10px); margin: 5px auto; text-align: center;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}
.node-title-input-inline:focus { outline: none; border-color: var(--accent); box-shadow: 0 0 0 2px rgba(var(--accent-rgb, 255, 126, 238), 0.3); }
.node-title-input-inline.scene-title-edit { font-weight: 600; }

.node-tags-container {
  display: flex; align-items: center; justify-content: space-between; gap: 0.5rem;
  margin-top: 6px; padding: 4px 0; width: 100%; max-width: calc(100% - 10px);
  font-size: 0.7rem; position: relative;
}
.tags-display-area { display: flex; flex-wrap: wrap; gap: 4px; flex-grow: 1; min-height: 18px; }
.tag-badge {
  background-color: var(--primary-dark); color: var(--text-light); padding: 2px 6px;
  border-radius: var(--border-radius-sm); font-size: 0.65rem; display: inline-flex;
  align-items: center; white-space: nowrap;
}
.no-tags-placeholder { opacity: 0.6; font-style: italic; }
.tag-remove-btn {
  background: none; border: none; color: var(--text-light); opacity: 0.7;
  margin-left: 4px; padding: 0; cursor: pointer; font-size: 0.8rem; line-height: 1;
}
.tag-remove-btn:hover { opacity: 1; color: var(--danger); }
.tag-editor-toggle {
  background: none; border: 1px solid var(--primary-light); color: var(--primary-light);
  padding: 2px 4px; font-size: 0.7rem; border-radius: var(--border-radius-sm);
  cursor: pointer; opacity: 0.8; flex-shrink: 0;
}
.tag-editor-toggle:hover { opacity: 1; background-color: var(--primary-dark); color: white; }
.tag-editor-popover {
  position: absolute; bottom: calc(100% + 5px); left: 50%; transform: translateX(-50%);
  background-color: var(--bg-light); border: 1px solid var(--primary-dark);
  border-radius: var(--border-radius-md); padding: 0.75rem; box-shadow: var(--shadow-hover);
  z-index: 20; width: 200px; display: flex; flex-direction: column; gap: 0.5rem;
}
.tag-editor-popover h5 {
  font-size: 0.8rem; color: var(--primary-light); margin: 0 0 0.5rem 0;
  padding-bottom: 0.3rem; border-bottom: 1px solid var(--primary-dark);
}
.current-tags-editable { display: flex; flex-wrap: wrap; gap: 5px; margin-bottom: 0.5rem; }
.add-tag-input-group { display: flex; gap: 0.3rem; }
.new-tag-input {
  flex-grow: 1; background-color: var(--bg-medium); color: var(--text-light);
  border: 1px solid var(--primary-dark); border-radius: var(--border-radius-sm);
  padding: 4px 6px; font-size: 0.75rem;
}
.new-tag-input:focus { outline: none; border-color: var(--accent); }
.add-tag-btn {
  background-color: var(--success); color: white; border: none; padding: 4px 8px;
  font-size: 0.7rem; border-radius: var(--border-radius-sm); cursor: pointer;
}
.add-tag-btn:hover { background-color: var(--success-dark); }

.node-video-info {
  font-size: 0.7rem; margin-top: 5px; color: #bbb; width: 100%;
  text-align: left; padding-left: 5px; white-space: nowrap;
  overflow: hidden; text-overflow: ellipsis;
}
.video-info-transition { opacity: 0.8; margin-left: 5px; }

/* AI Results styling */
.node-ai-results {
  margin-top: 6px;
  padding: 4px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
  font-size: 0.65rem;
}

.ai-results-header {
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 2px;
}

.ai-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1px 2px;
  margin: 1px 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.ai-result-type {
  color: #81C784;
  text-transform: capitalize;
}

.ai-result-status {
  color: #4CAF50;
}

/* Edge Interaction */
.edge-interaction-wrapper {}
.edge-delete-button {
  background-color: var(--danger); color: var(--text-light); border: none; width: 20px; height: 20px;
  border-radius: 50%; cursor: pointer; font-size: 12px; line-height: 20px; text-align: center;
  opacity: 0; transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out; box-shadow: var(--shadow);
}
.react-flow__edge:hover .edge-delete-button { opacity: 1; }
.edge-delete-button:hover { background-color: var(--danger-dark); transform: scale(1.1); }

/* SceneList Styling */
.scene-list-container { display: flex; flex-direction: column; gap: 0.5rem; }
.scene-list-title {
  font-size: 0.8rem; color: var(--text-light); opacity: 0.8; margin-bottom: 0.3rem;
  padding-bottom: 0.3rem; border-bottom: 1px solid var(--primary-dark); font-weight: 500;
}
.no-scenes-message { font-size: 0.75rem; color: var(--text-light); opacity: 0.6; text-align: center; padding: 1rem 0.5rem; }
.scene-list { list-style: none; padding: 0; margin: 0; max-height: 300px; overflow-y: auto; }
.scene-list::-webkit-scrollbar { width: 5px; }
.scene-list::-webkit-scrollbar-thumb { background-color: var(--primary-dark); border-radius: 3px; }
.scene-list::-webkit-scrollbar-track { background-color: var(--bg-light); }
.scene-list-item {
  display: flex; align-items: center; gap: 0.5rem; padding: 0.4rem 0.5rem;
  border-radius: var(--border-radius-sm); cursor: pointer; transition: background-color 0.15s ease;
  font-size: 0.75rem; border-bottom: 1px solid var(--bg-light);
}
.scene-list-item:last-child { border-bottom: none; }
.scene-list-item:hover { background-color: var(--bg-light); }
.scene-list-item.selected { background-color: var(--primary-dark); color: white; font-weight: 500; }
.scene-item-index { opacity: 0.7; min-width: 25px; }
.scene-item-timecode { flex-grow: 1; }
.scene-item-duration { opacity: 0.7; font-size: 0.7rem; }
.scene-item-transition { font-size: 0.8rem; min-width: 20px; text-align: center; }

/* ScenePreview Styling */
.scene-preview-placeholder {
  display: flex; align-items: center; justify-content: center; height: 200px;
  background-color: var(--bg-light); border-radius: var(--border-radius-md);
  margin-top: 1rem; color: var(--text-light); opacity: 0.7; font-size: 0.9rem;
}
.content-area-with-preview > .scene-preview-container,
.content-area-with-preview > .scene-preview-placeholder {
  flex-shrink: 0;
  margin-top: 0.5rem;
}
.scene-preview-container {
  background-color: var(--bg-icon-bar); padding: 0.75rem;
  border-radius: var(--border-radius-md); box-shadow: var(--shadow);
}
.scene-preview-video { width: 100%; max-height: 360px; border-radius: var(--border-radius-sm); background-color: #000; }
.scene-preview-controls {
  display: flex; align-items: center; gap: 0.75rem;
  padding-top: 0.5rem; margin-top: 0.5rem; border-top: 1px solid var(--bg-light);
}
.preview-button {
  background-color: var(--primary); color: white; border: none; border-radius: 50%;
  width: 36px; height: 36px; font-size: 1rem; cursor: pointer;
  display: flex; align-items: center; justify-content: center; transition: background-color 0.2s ease;
}
.preview-button:hover { background-color: var(--primary-light); }
.preview-button:disabled { background-color: var(--bg-light); opacity: 0.5; cursor: not-allowed; }
.timeline-slider { flex-grow: 1; accent-color: var(--primary); cursor: pointer; }
.timeline-slider::-webkit-slider-thumb {
  -webkit-appearance: none; appearance: none; width: 14px; height: 14px;
  background: var(--accent); border-radius: 50%; cursor: pointer;
}
.timeline-slider::-moz-range-thumb {
  width: 14px; height: 14px; background: var(--accent);
  border-radius: 50%; cursor: pointer; border: none;
}
.time-display { font-size: 0.75rem; color: var(--text-light); opacity: 0.8; min-width: 45px; text-align: center; }
.loading-indicator { font-size: 0.8rem; color: var(--warning); }

/* Toast & Error Styling */
.toast-notification {
  position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
  background-color: var(--warning); color: var(--text-dark); padding: 10px 20px;
  border-radius: var(--border-radius-sm); box-shadow: var(--shadow-hover);
  z-index: 1001; font-size: 0.9rem; animation: fadeInOut 3s ease-in-out;
}
@keyframes fadeInOut {
  0%, 100% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
  10%, 90% { opacity: 1; transform: translateX(-50%) translateY(0); }
}
.loading-text { font-size: 0.85rem; color: var(--warning); text-align: center; }
.error-message {
  background-color: rgba(231, 76, 60, 0.1); color: var(--danger); padding: 0.75rem;
  border-radius: var(--border-radius-sm); font-size: 0.85rem; border: 1px solid var(--danger);
}
.error-message p { margin-bottom: 0.5rem; }
.error-message button { font-size: 0.8rem; padding: 0.4rem 0.8rem; }

.scene-search-filter-container {
  position: relative; /* For dropdown positioning */
  margin-bottom: 1rem; /* Space below if in sidebar panel */
}

.search-bar-wrapper {
  display: flex;
  align-items: center;
  background-color: var(--bg-light);
  border: 1px solid var(--bg-medium);
  border-radius: var(--border-radius-md);
  padding: 0.3rem 0.6rem;
  transition: border-color 0.2s ease;
}
.search-bar-wrapper.focused {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb, 138, 43, 226), 0.2);
}
.search-icon {
  color: var(--text-light);
  opacity: 0.7;
  margin-right: 0.5rem;
  cursor: text; /* To indicate input focus */
  font-size: 0.9rem;
}
.search-input {
  flex-grow: 1;
  background-color: transparent;
  color: var(--text-light);
  border: none;
  outline: none;
  font-size: 0.85rem;
  padding: 0.2rem 0;
}
.search-input::placeholder {
  color: var(--text-light);
  opacity: 0.5;
}
.clear-search-btn {
  background: none;
  border: none;
  color: var(--text-light);
  opacity: 0.6;
  cursor: pointer;
  font-size: 1.1rem;
  padding: 0 0.3rem;
  line-height: 1;
}
.clear-search-btn:hover {
  opacity: 1;
  color: var(--danger);
}

.search-dropdown-content {
  /* Positioned absolutely if it's a global search, or relatively if in sidebar */
  background-color: var(--bg-medium);
  border: 1px solid var(--bg-light);
  border-top: none;
  border-bottom-left-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
  padding: 0.75rem;
  margin-top: -1px; /* Overlap with search bar bottom border */
  box-shadow: var(--shadow);
  max-height: 400px; /* Or adjust as needed */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.search-dropdown-content::-webkit-scrollbar { width: 6px; }
.search-dropdown-content::-webkit-scrollbar-thumb { background-color: var(--primary-dark); border-radius: 3px; }
.search-dropdown-content::-webkit-scrollbar-track { background-color: var(--bg-light); }


.filter-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--bg-light);
  margin-bottom: 0.5rem;
}
.filter-tag-chip {
  background-color: var(--bg-light);
  color: var(--text-light);
  opacity: 0.8;
  border: 1px solid var(--primary-dark);
  padding: 0.2rem 0.6rem;
  border-radius: 12px; /* Pill shape */
  font-size: 0.7rem;
  cursor: pointer;
  transition: var(--transition);
}
.filter-tag-chip:hover {
  opacity: 1;
  background-color: var(--primary-dark);
  border-color: var(--primary);
}
.filter-tag-chip.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary-light);
  opacity: 1;
}
.clear-filters-btn {
  background: none;
  border: none;
  color: var(--accent);
  font-size: 0.7rem;
  text-decoration: underline;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  margin-left: auto; /* Pushes to the right */
}
.clear-filters-btn:hover { color: var(--primary-light); }

.search-navigation-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-light);
  opacity: 0.8;
  padding: 0.2rem 0;
}
.search-navigation-controls button {
  background: none;
  border: 1px solid var(--primary-dark);
  color: var(--text-light);
  border-radius: var(--border-radius-sm);
  padding: 0.1rem 0.4rem;
  cursor: pointer;
}
.search-navigation-controls button:hover { background-color: var(--bg-light); }


.search-results-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}
.search-result-item {
  padding: 0.5rem 0.6rem;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: background-color 0.15s ease;
  border: 1px solid transparent; /* For consistent height */
}
.search-result-item:hover {
  background-color: var(--bg-light);
}
.search-result-item.active {
  background-color: var(--primary-dark);
  border-color: var(--primary);
  color: white;
}
.result-scene-number {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-right: 0.5rem;
  display: inline-block;
  width: 25px; /* Align numbers */
}
.result-title {
  font-size: 0.8rem;
  font-weight: 500;
}
.result-tags {
  margin-top: 0.2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}
.result-tags .tag-badge.small {
  font-size: 0.6rem;
  padding: 1px 4px;
  background-color: var(--bg-light); /* Different from node tags */
}

.no-results-message {
  font-size: 0.8rem;
  color: var(--text-light);
  opacity: 0.6;
  text-align: center;
  padding: 1rem 0.5rem;
}

/* Node Highlighting for Search/Filter */
.story-node.dimmed-node {
  opacity: 0.4;
  /* filter: grayscale(50%); Optional */
  transition: opacity 0.3s ease, filter 0.3s ease;
}
.story-node.search-highlight {
  /* Same as .selected or a bit more prominent */
  box-shadow: 0 0 0 3px var(--success), var(--shadow-hover) !important;
  /* border-color: var(--success) !important; */
  transform: scale(1.04) !important;
  opacity: 1 !important; /* Ensure highlighted nodes are fully visible */
}
.story-node.selected.search-highlight {
    /* If already selected and also a search highlight, make it stand out even more or keep it as is */
    box-shadow: 0 0 0 2px var(--accent), 0 0 0 5px var(--success), var(--shadow-hover) !important;
}


/* Temp message for no results (like a soft toast) */
.temp-no-results-ux {
  position: absolute;
  bottom: 20px; /* Or top, depending on where search is */
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--bg-medium);
  color: var(--text-light);
  padding: 8px 15px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  box-shadow: var(--shadow);
  opacity: 0;
  animation: fadeInOutSoft 3s ease-in-out forwards;
  z-index: 500; /* Above ReactFlow pane */
}
.interactive-scene-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; /* High z-index to be on top */
  backdrop-filter: blur(3px); /* Optional: blur background */
}

.interactive-scene-modal-content {
  background-color: var(--bg-medium);
  padding: 20px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-hover);
  width: 80%;
  max-width: 700px; /* Max width of the modal */
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 1rem;
}

.modal-close-button {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
  opacity: 0.7;
}
.modal-close-button:hover {
  opacity: 1;
}

.interactive-scene-modal-content h4 {
  color: var(--primary-light);
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--primary-dark);
  padding-bottom: 0.5rem;
}

.video-player-container {
  width: 100%;
  background-color: #000;
  border-radius: var(--border-radius-sm);
  overflow: hidden; /* Ensures video corners are rounded if video itself isn't */
}

.modal-video-player {
  width: 100%;
  display: block; /* Removes extra space below video */
  max-height: 50vh; /* Limit video height */
  border-radius: var(--border-radius-sm);
}

.modal-video-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  border-top: 1px solid var(--bg-light);
}

/* Re-use existing .preview-button, .timeline-slider, .time-display, .loading-indicator styles */
/* Ensure they are general enough or duplicate/prefix them if needed */

/* CustomSceneNode button to open modal */
.node-preview-button {
  position: absolute;
  bottom: 5px;
  left: 5px; /* Or another position */
  background-color: rgba(var(--primary-rgb, 138, 43, 226), 0.2);
  color: var(--primary-light);
  border: 1px solid var(--primary-dark);
  border-radius: var(--border-radius-sm);
  padding: 3px 6px;
  font-size: 0.7rem;
  cursor: pointer;
  opacity: 0.8;
  transition: var(--transition);
  z-index: 10;
}
.node-preview-button:hover {
  opacity: 1;
  background-color: rgba(var(--primary-rgb, 138, 43, 226), 0.4);
  color: white;
}

/* Adjustments for selected node to show the button more clearly */
.story-node.selected .node-preview-button {
    opacity: 1;
}

@keyframes fadeInOutSoft {
  0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
  10% { opacity: 1; transform: translateX(-50%) translateY(0); }
  90% { opacity: 1; transform: translateX(-50%) translateY(0); }
  100% { opacity: 0; transform: translateX(-50%) translateY(10px); }
}
.api-health-banner {
  padding: 10px;
  text-align: center;
  font-weight: 500;
  position: sticky; /* Or fixed if you want it always on top */
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1005; /* Above toasts */
}
.api-health-banner.error {
  background-color: var(--danger);
  color: white;
}

.toast-container {
  position: fixed;
  top: 20px; /* Adjust if you have a health banner */
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.toast-notification {
  /* ... existing styles ... */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  min-width: 250px;
  max-width: 500px;
}
.toast-notification.info { background-color: var(--info); color: white;}
.toast-notification.success { background-color: var(--success); color: white;}
.toast-notification.warning { background-color: var(--warning); color: var(--text-dark); }
.toast-notification.error { background-color: var(--danger); color: white; }


.toast-retry-btn {
  padding: 4px 8px;
  font-size: 0.75rem;
  margin-left: auto; /* Pushes retry to the right of message */
  background-color: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.4);
  color: inherit;
  border-radius: var(--border-radius-sm);
}
.toast-retry-btn:hover {
  background-color: rgba(255,255,255,0.3);
}
.toast-close-btn {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  font-size: 1.2rem;
  padding: 0 5px;
  line-height: 1;
}
.toast-close-btn:hover {
  opacity: 1;
}

/* Node error state */
.story-node.has-error {
  border: 2px dashed var(--danger) !important;
  box-shadow: 0 0 8px rgba(var(--danger-rgb, 231, 76, 60), 0.5) !important;
}
.node-error-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--danger);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: var(--shadow);
  z-index: 15;
}
.scene-preview-editor-style {
  display: flex;
  flex-direction: column;
  background-color: #2e2e2e; /* Dark editor-like background */
  color: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  min-height: 150px; /* Adjust as needed for more controls */
  box-sizing: border-box;
}

.video-display-area {
  position: relative; /* For overlay messages */
  background-color: #000;
  margin-bottom: 10px;
  min-height: 100px; /* Or make it aspect ratio controlled */
}

.scene-preview-video-element {
  width: 100%;
  display: block; /* Remove extra space below video */
  max-height: 300px; /* Example max height, adjust as needed */
}

.video-error-overlay,
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.9em;
}
.video-error-overlay {
  color: #ff8a8a;
}

.timeline-controls-area {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Spacing between timeline and button/timecode rows */
}

.timeline-track-container {
  width: 100%;
  height: 20px; /* Height of the clickable timeline area */
  background-color: #1c1c1c; /* Darker track background */
  border-radius: 3px;
  cursor: pointer;
  position: relative; /* For playhead positioning */
  padding: 5px 0; /* Vertical padding to make it easier to click */
  box-sizing: content-box; /* Padding doesn't add to height used for playhead calc */
}

.timeline-track {
    height: 10px; /* Actual visual track height */
    background-color: #4a4a4a; /* Color of the track itself */
    border-radius: 2px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

.timeline-playhead {
  position: absolute;
  top: -2px; /* Center vertically over the track-container height */
  width: 4px; /* Width of the playhead */
  height: 24px; /* Height of the playhead */
  background-color: #00aeff; /* Bright color for playhead */
  border-radius: 1px;
  transform: translateX(-50%); /* Center the playhead on its 'left' position */
  z-index: 10;
  pointer-events: none; /* So clicks go through to the track-container */
}

.playback-buttons-panel {
  display: flex;
  justify-content: center; /* Or flex-start */
  align-items: center;
  gap: 8px;
}

.control-btn {
  background-color: #4f4f4f;
  color: white;
  border: 1px solid #666;
  padding: 6px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.9em;
  min-width: 40px; /* For consistent button size */
  text-align: center;
}
.control-btn:hover:not(:disabled) {
  background-color: #5a5a5a;
}
.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.play-pause-btn {
  font-size: 1.1em; /* Make play/pause slightly larger */
}

.timecode-display-panel {
  display: flex;
  justify-content: flex-end; /* Align time to the right */
  align-items: center;
  font-size: 0.85em;
  color: #ccc;
  gap: 4px;
  padding-right: 5px;
}
.timecode {
  font-family: 'monospace'; /* For consistent digit width */
}
.scene-preview-placeholder {
    padding: 20px;
    text-align: center;
    color: #aaa;
}
.interactive-scene-modal-content {
  /* ... existing styles ... */
  min-height: 400px; /* Example: adjust as needed */
  display: flex;
  flex-direction: column;
}

.interactive-scene-modal-content .modal-title-header {
    margin-bottom: 15px; /* Add some space below title */
    text-align: center;
}

/* The styles from the previous ScenePreview.tsx example should follow, e.g.: */
.video-display-area {
  position: relative;
  background-color: #000;
  margin-bottom: 10px;
  min-height: 150px; /* Or control by aspect ratio */
  flex-shrink: 0; /* Prevent video area from shrinking too much */
}

.scene-preview-video-element { /* Renamed class from modal-video-player for consistency */
  width: 100%;
  display: block;
  max-height: 350px; /* Adjust as needed */
  background-color: #111; /* Darker background while loading */
}

.timeline-controls-area {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Spacing between timeline, buttons, timecode */
    padding-top: 10px; /* Space above timeline */
    flex-shrink: 0;
}

.interactive-scene-modal-content {
  /* ... existing styles ... */
  width: 90%; /* Make modal wider for editor */
  max-width: 1200px; /* Adjust as needed */
  height: 85vh; /* Make modal taller */
  max-height: 800px; /* Adjust as needed */
  display: flex; /* Already there, good */
  flex-direction: column; /* Already there, good */
}

.interactive-scene-modal-content .modal-title-header {
    margin-bottom: 10px; /* Adjust spacing */
    text-align: left;
    flex-shrink: 0; /* Prevent title from shrinking */
}

/* Ensure the container for AdvancedSceneEditor (if you add one inside modal-content)
   or the modal-content itself allows the editor to grow */
.interactive-scene-modal-content > .advanced-scene-editor-container-if-any {
  flex-grow: 1;
  overflow: hidden; /* Editor handles its own scrolling */
}

.node-delete-button, .node-connect-button, .node-preview-button {
  position: absolute;
  top: 5px;
  width: 24px;
  height: 24px;
  font-size: 14px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 4px;
  color: inherit;
  cursor: pointer;
  transition: background-color 0.2s;
}
.node-delete-button { right: 5px; }
.node-connect-button { right: 35px; }
.node-preview-button { right: 65px; }
.node-delete-button:hover,
.node-connect-button:hover,
.node-preview-button:hover {
  background: rgba(255,255,255,0.2);
}

/* Adjust title padding for space */
.node-title {
  padding-right: 70px; /* Leaves space for 3 buttons */
  cursor: default;
}

/* Position badge top-left */
.node-type-badge {
  top: -10px;
  left: -10px;
  position: absolute;
  padding: 2px 6px;
  font-size: 0.7em;
  border-radius: 4px;
  text-transform: capitalize;
}

/* Tag editor popover position structure */
.tag-editor-popover {
  right: 5px;
  top: calc(100% + 5px);
  position: absolute;
  width: 220px;
  box-shadow: var(--shadow-hover);
}