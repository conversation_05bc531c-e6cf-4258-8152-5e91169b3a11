#!/bin/bash

# Deployment script for Hybrid Strategy (Vercel + GCP)
# This script deploys the backend to Google Cloud Run and frontend to Vercel

set -e

echo "🚀 Starting Hybrid Deployment (Vercel + GCP)"
echo "============================================="

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-"cryptic-lattice-463111-k6"}
REGION=${GCP_REGION:-"asia-southeast1"}
BUCKET_NAME=${GCS_BUCKET_NAME:-"insomnia_bucket_38"}
SERVICE_NAME="insomnia-backend"

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI not found. Please install Google Cloud SDK."
    exit 1
fi

if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Please install with: npm i -g vercel"
    exit 1
fi

# Authenticate with Google Cloud
echo "🔐 Authenticating with Google Cloud..."
gcloud auth login --brief
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable storage.googleapis.com

# Create storage bucket if it doesn't exist
echo "🪣 Setting up Google Cloud Storage..."
if ! gsutil ls -b gs://$BUCKET_NAME &> /dev/null; then
    echo "Creating bucket: $BUCKET_NAME"
    gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME
    
    # Set bucket permissions for public read
    gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME
    
    # Enable CORS for the bucket
    echo '[{"origin":["*"],"method":["GET","POST","PUT","DELETE"],"responseHeader":["Content-Type","Access-Control-Allow-Origin"],"maxAgeSeconds":3600}]' > cors.json
    gsutil cors set cors.json gs://$BUCKET_NAME
    rm cors.json
else
    echo "Bucket $BUCKET_NAME already exists"
fi

# Deploy backend to Google Cloud Run
echo "☁️ Deploying backend to Google Cloud Run..."
cd backend

# Test Docker build locally first (optional)
read -p "Test Docker build locally first? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧪 Testing Docker build locally..."
    if docker build -t test-build .; then
        echo "✅ Local Docker build successful."
        docker rmi test-build 2>/dev/null || true
    else
        echo "❌ Local Docker build failed."
        echo "💡 Will try with minimal requirements during Cloud Build..."
        docker rmi test-build 2>/dev/null || true
    fi
fi

# Build and deploy using Cloud Build
echo "🏗️ Building with Cloud Build..."
if ! gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME; then
    echo "❌ Cloud Build failed. Trying with minimal requirements..."

    # Backup original and use minimal requirements
    cp requirements.txt requirements.txt.backup
    cp requirements-minimal.txt requirements.txt

    if gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME; then
        echo "✅ Build successful with minimal requirements"
        # Restore original requirements for future builds
        cp requirements.txt.backup requirements.txt
    else
        echo "❌ Build failed even with minimal requirements"
        # Restore original requirements
        cp requirements.txt.backup requirements.txt
        exit 1
    fi
fi

# Deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8080 \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --max-instances 10 \
    --set-env-vars="CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=$BUCKET_NAME,GCP_PROJECT_ID=$PROJECT_ID"

# Get the backend URL
BACKEND_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
echo "✅ Backend deployed to: $BACKEND_URL"

cd ..

# Update vercel.json with the actual backend URL
echo "🔧 Updating Vercel configuration..."
sed -i.bak "s|https://your-backend-url.run.app|$BACKEND_URL|g" vercel.json

# Deploy frontend to Vercel
echo "🌐 Deploying frontend to Vercel..."
export VITE_API_URL=$BACKEND_URL
export VITE_ENVIRONMENT=production

# Build and deploy to Vercel
vercel --prod --yes

echo ""
echo "🎉 Hybrid deployment completed successfully!"
echo "============================================="
echo "Backend URL: $BACKEND_URL"
echo "Frontend: Check Vercel dashboard for URL"
echo ""
echo "Next steps:"
echo "1. Test the application functionality"
echo "2. Set up monitoring and alerts"
echo "3. Configure custom domain (optional)"
echo ""
