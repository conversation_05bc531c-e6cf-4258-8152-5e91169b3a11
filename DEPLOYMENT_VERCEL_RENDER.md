# 🚀 Deployment Guide: Vercel (Frontend) + Render.com (Backend)

This guide will help you deploy the Insomnia video editing application with the frontend on Vercel and the backend on Render.com.

## 📋 Prerequisites

- GitHub account
- Vercel account (free tier available)
- Render.com account (free tier available)
- Your project code pushed to a GitHub repository

## 🔧 Backend Deployment on Render.com

### Step 1: Prepare Backend for Production

1. **Create requirements.txt** (if not exists):
```bash
cd backend
pip freeze > requirements.txt
```

2. **Create render.yaml** in the backend directory:
```yaml
services:
  - type: web
    name: insomnia-backend
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: PYTHON_VERSION
        value: 3.12.3
      - key: PORT
        generateValue: true
```

3. **Update main.py CORS settings**:
```python
# Update CORS middleware in backend/main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", 
        "http://127.0.0.1:5173",
        "https://*.vercel.app",  # Allow all Vercel domains
        "https://your-app-name.vercel.app"  # Replace with your actual domain
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Step 2: Deploy Backend to Render.com

1. **Login to Render.com** and click "New +"
2. **Select "Web Service"**
3. **Connect your GitHub repository**
4. **Configure the service**:
   - **Name**: `insomnia-backend`
   - **Environment**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `uvicorn main:app --host 0.0.0.0 --port $PORT`
   - **Plan**: Free (or paid for better performance)

5. **Set Environment Variables**:
   - `PYTHON_VERSION`: `3.12.3`
   - Add any API keys you need (GEMINI_API_KEY, etc.)

6. **Deploy**: Click "Create Web Service"

7. **Note your backend URL**: `https://your-service-name.onrender.com`

### Step 3: Test Backend Deployment

Once deployed, test your backend:
```bash
curl https://your-service-name.onrender.com/api/health
```

You should get: `{"status":"ok","timestamp":"2024-test","server":"uvicorn-fresh"}`

## 🌐 Frontend Deployment on Vercel

### Step 1: Prepare Frontend for Production

1. **Create environment configuration** in `src/config/environment.ts`:
```typescript
// src/config/environment.ts
export const API_CONFIG = {
  development: {
    baseURL: 'http://localhost:8000',
  },
  production: {
    baseURL: 'https://your-service-name.onrender.com', // Replace with your Render URL
  }
};

export const getApiBaseUrl = (): string => {
  const isDevelopment = process.env.NODE_ENV === 'development' || 
                       window.location.hostname === 'localhost';
  
  return isDevelopment ? API_CONFIG.development.baseURL : API_CONFIG.production.baseURL;
};
```

2. **Update App.tsx to use production API**:
```typescript
// In src/App.tsx, replace hardcoded localhost URLs
import { getApiBaseUrl } from './config/environment';

// Replace this line:
// const response = await fetch('http://localhost:8000/api/health');
// With this:
const response = await fetch(`${getApiBaseUrl()}/api/health`);
```

3. **Update vite.config.ts for production**:
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json']
  },
  server: {
    proxy: {
      '/api': {
        target: process.env.VITE_API_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu']
        }
      }
    }
  }
})
```

### Step 2: Deploy Frontend to Vercel

1. **Login to Vercel** and click "New Project"
2. **Import your GitHub repository**
3. **Configure the project**:
   - **Framework Preset**: Vite
   - **Root Directory**: `./` (or leave empty if repo root)
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

4. **Set Environment Variables**:
   - `VITE_API_URL`: `https://your-service-name.onrender.com`
   - `VITE_GEMINI_API_KEY`: Your Gemini API key
   - Any other environment variables your app needs

5. **Deploy**: Click "Deploy"

6. **Note your frontend URL**: `https://your-app-name.vercel.app`

### Step 3: Update Backend CORS for Frontend URL

1. **Go back to your backend code** and update the CORS origins:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", 
        "http://127.0.0.1:5173",
        "https://your-app-name.vercel.app",  # Your actual Vercel URL
        "https://*.vercel.app"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

2. **Redeploy your backend** on Render.com

## 🔧 Configuration Files Summary

### Backend Files to Modify:
- `backend/main.py` - Update CORS origins
- `backend/requirements.txt` - Ensure all dependencies are listed
- `backend/render.yaml` - Render.com configuration (optional)

### Frontend Files to Create/Modify:
- `src/config/environment.ts` - API configuration
- `src/App.tsx` - Update API calls to use environment config
- `vite.config.ts` - Production build configuration

## 🧪 Testing Your Deployment

1. **Test Backend Health**:
```bash
curl https://your-service-name.onrender.com/api/health
```

2. **Test Frontend**:
   - Visit `https://your-app-name.vercel.app`
   - Check browser console for any CORS errors
   - Test video upload and AI processing

3. **Test Full Integration**:
   - Upload a video
   - Add AI agents
   - Connect agents to scenes
   - Verify real-time processing works

## 🚨 Common Issues & Solutions

### CORS Errors
- Ensure backend CORS includes your Vercel domain
- Check that both HTTP and HTTPS are handled correctly

### API Connection Issues
- Verify environment variables are set correctly
- Check that API URLs don't have trailing slashes

### Build Failures
- Ensure all dependencies are in package.json
- Check that TypeScript types are correct
- Verify environment variables are available during build

### Performance Issues
- Consider upgrading Render.com plan for better backend performance
- Optimize frontend bundle size
- Use Vercel's Edge Functions if needed

## 💰 Cost Considerations

### Free Tier Limitations:
- **Render.com Free**: 750 hours/month, sleeps after 15 minutes of inactivity
- **Vercel Free**: 100GB bandwidth, 6000 minutes build time

### Recommended Upgrades:
- **Render.com Starter ($7/month)**: No sleep, better performance
- **Vercel Pro ($20/month)**: More bandwidth, faster builds

## 🔄 Continuous Deployment

Both platforms support automatic deployment:
- **Vercel**: Automatically deploys on git push to main branch
- **Render.com**: Automatically deploys on git push to main branch

## 📞 Support

If you encounter issues:
1. Check the deployment logs on both platforms
2. Verify all environment variables are set
3. Test API endpoints individually
4. Check browser network tab for failed requests

Your application should now be fully deployed and accessible worldwide! 🌍
