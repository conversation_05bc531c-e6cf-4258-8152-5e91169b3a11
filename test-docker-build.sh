#!/bin/bash

# Test Docker build locally before deploying to GCP
set -e

echo "🐳 Testing Docker Build Locally"
echo "==============================="

# Configuration
IMAGE_NAME="insomnia-backend-test"
CONTAINER_NAME="insomnia-test"

# Cleanup any existing containers/images
echo "🧹 Cleaning up existing test containers..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build the Docker image
echo "🏗️ Building Docker image..."
cd backend

if docker build -t $IMAGE_NAME .; then
    echo "✅ Docker build successful!"
else
    echo "❌ Docker build failed!"
    echo ""
    echo "💡 Troubleshooting tips:"
    echo "1. Check if all dependencies in requirements.txt are available"
    echo "2. Try building with minimal requirements:"
    echo "   cp requirements-minimal.txt requirements.txt && docker build -t $IMAGE_NAME ."
    echo "3. Check Docker logs for specific error messages"
    exit 1
fi

# Test running the container
echo "🚀 Testing container startup..."
if docker run -d --name $CONTAINER_NAME -p 8080:8080 \
    -e CLOUD_STORAGE_ENABLED=false \
    $IMAGE_NAME; then
    
    echo "✅ Container started successfully!"
    
    # Wait for the service to be ready
    echo "⏳ Waiting for service to be ready..."
    sleep 10
    
    # Test the health endpoint
    if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
        echo "✅ Health check passed!"
        
        # Get health response
        HEALTH_RESPONSE=$(curl -s http://localhost:8080/api/health)
        echo "📊 Health response: $HEALTH_RESPONSE"
    else
        echo "❌ Health check failed!"
        echo "📋 Container logs:"
        docker logs $CONTAINER_NAME
    fi
    
    # Cleanup
    echo "🧹 Cleaning up test container..."
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
    
else
    echo "❌ Container failed to start!"
    echo "📋 Container logs:"
    docker logs $CONTAINER_NAME 2>/dev/null || echo "No logs available"
    exit 1
fi

echo ""
echo "🎉 Docker build test completed successfully!"
echo "You can now deploy to GCP with confidence."
echo ""
echo "Next steps:"
echo "1. Run: ./deploy-hybrid.sh (for Vercel + GCP)"
echo "2. Or run: ./deploy-gcp-only.sh (for GCP-only)"

cd ..
