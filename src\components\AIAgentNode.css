/* src/components/AIAgentNode.css */

.ai-agent-node {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-agent-node:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.ai-agent-node.selected {
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

.ai-agent-node.dimmed {
  opacity: 0.4;
  filter: grayscale(50%);
}

.ai-agent-node.search-highlight {
  border-color: #FF9800;
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.4);
}

/* Agent Header */
.ai-agent-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  position: relative;
}

.ai-agent-icon {
  font-size: 18px;
  margin-right: 8px;
}

.ai-agent-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ai-agent-delete-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.ai-agent-delete-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Agent Body */
.ai-agent-body {
  padding: 12px;
  background: white;
}

.ai-agent-status {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.status-icon {
  margin-right: 6px;
  font-size: 14px;
}

.status-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* Input Connections */
.input-connections {
  margin: 8px 0;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 11px;
  color: #666;
  text-align: center;
}

/* Process Button */
.process-btn {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.process-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.process-btn:active {
  transform: translateY(0);
}

/* Output Summary */
.output-summary {
  margin-top: 8px;
  padding: 6px 8px;
  background: #e8f5e8;
  border-radius: 4px;
  font-size: 11px;
  color: #2e7d32;
}

.confidence {
  margin-top: 4px;
  font-weight: 600;
}

/* Error Message */
.error-message {
  margin-top: 8px;
  padding: 6px 8px;
  background: #ffebee;
  border-radius: 4px;
  font-size: 11px;
  color: #c62828;
}

/* Handles */
.ai-agent-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.ai-agent-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.input-handle {
  left: -6px;
}

.output-handle {
  right: -6px;
}

/* Status-specific styles */
.ai-agent-node.processing {
  border-color: #FF9800;
}

.ai-agent-node.processing .ai-agent-header {
  background: linear-gradient(90deg, #FF9800 0%, #FFB74D 100%);
}

.ai-agent-node.completed {
  border-color: #4CAF50;
}

.ai-agent-node.completed .ai-agent-header {
  background: linear-gradient(90deg, #4CAF50 0%, #81C784 100%);
}

.ai-agent-node.error {
  border-color: #F44336;
}

.ai-agent-node.error .ai-agent-header {
  background: linear-gradient(90deg, #F44336 0%, #EF5350 100%);
}

/* Animation for processing state */
.ai-agent-node.processing .ai-agent-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ai-agent-node {
    min-width: 180px;
    max-width: 200px;
  }
  
  .ai-agent-name {
    font-size: 13px;
  }
  
  .ai-agent-body {
    padding: 10px;
  }
}
