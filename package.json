{"name": "storyboard-os", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@designcombo/events": "^1.0.2", "@designcombo/frames": "^0.0.3", "@designcombo/state": "3.1.13", "@designcombo/timeline": "3.1.13", "@designcombo/types": "3.1.13", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^3.4.2", "@interactify/infinite-viewer": "^0.0.2", "@interactify/moveable": "0.0.2", "@interactify/selection": "^0.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@remotion/media-utils": "4.0.221", "@remotion/paths": "4.0.221", "@remotion/player": "4.0.221", "@remotion/shapes": "4.0.221", "@remotion/transitions": "4.0.221", "@tabler/icons-react": "^3.5.0", "@types/tinycolor2": "^1.4.6", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.5.6", "fuse.js": "^7.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.441.0", "non.geist": "^1.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.5", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "reactflow": "^11.11.4", "remotion": "4.0.221", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tinycolor2": "^1.6.0", "uuid": "^11.1.0", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash": "^4.17.9", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.20", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.47", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.12", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}