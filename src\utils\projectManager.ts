export interface ProjectMetadata {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
  thumbnail?: string;
  videoFileName?: string;
  duration?: number;
  sceneCount?: number;
}

export class ProjectManager {
  private static readonly PROJECTS_KEY = 'storyboard-projects';
  private static readonly MAX_RECENT_PROJECTS = 10;

  static getAllProjects(): ProjectMetadata[] {
    try {
      const saved = localStorage.getItem(this.PROJECTS_KEY);
      if (!saved) return [];
      
      return JSON.parse(saved).map((p: any) => ({
        ...p,
        createdAt: new Date(p.createdAt),
        lastModified: new Date(p.lastModified)
      }));
    } catch (error) {
      console.error('Error loading projects:', error);
      return [];
    }
  }

  static saveProject(project: ProjectMetadata): void {
    try {
      const projects = this.getAllProjects();
      const existingIndex = projects.findIndex(p => p.id === project.id);
      
      if (existingIndex >= 0) {
        projects[existingIndex] = project;
      } else {
        projects.unshift(project);
      }

      // Keep only the most recent projects
      const trimmedProjects = projects
        .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
        .slice(0, this.MAX_RECENT_PROJECTS);

      localStorage.setItem(this.PROJECTS_KEY, JSON.stringify(trimmedProjects));
    } catch (error) {
      console.error('Error saving project:', error);
    }
  }

  static deleteProject(projectId: string): void {
    try {
      const projects = this.getAllProjects();
      const filtered = projects.filter(p => p.id !== projectId);
      localStorage.setItem(this.PROJECTS_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  }

  static createNewProject(name: string, description?: string): ProjectMetadata {
    const project: ProjectMetadata = {
      id: `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      createdAt: new Date(),
      lastModified: new Date()
    };

    this.saveProject(project);
    return project;
  }

  static updateProjectMetadata(projectId: string, updates: Partial<ProjectMetadata>): void {
    try {
      const projects = this.getAllProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex >= 0) {
        projects[projectIndex] = {
          ...projects[projectIndex],
          ...updates,
          lastModified: new Date()
        };
        localStorage.setItem(this.PROJECTS_KEY, JSON.stringify(projects));
      }
    } catch (error) {
      console.error('Error updating project metadata:', error);
    }
  }

  static getRecentProjects(limit: number = 5): ProjectMetadata[] {
    return this.getAllProjects()
      .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
      .slice(0, limit);
  }
}
