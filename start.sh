#!/bin/bash

# Start script for full-stack GCP deployment

# Function to handle shutdown
shutdown() {
    echo "Shutting down..."
    kill $BACKEND_PID $NGINX_PID
    wait $BACKEND_PID $NGINX_PID
    exit 0
}

# Trap signals
trap shutdown SIGTERM SIGINT

# Start the backend server
echo "Starting backend server..."
cd /app/backend
python main.py &
BACKEND_PID=$!

# Wait for backend to be ready
echo "Waiting for backend to start..."
for i in {1..30}; do
    if curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
        echo "Backend is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "Backend failed to start within 30 seconds"
        exit 1
    fi
    sleep 1
done

# Start nginx
echo "Starting nginx..."
nginx -g "daemon off;" &
NGINX_PID=$!

# Wait for nginx to be ready
echo "Waiting for nginx to start..."
for i in {1..10}; do
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "Nginx is ready!"
        break
    fi
    if [ $i -eq 10 ]; then
        echo "Nginx failed to start within 10 seconds"
        exit 1
    fi
    sleep 1
done

echo "Application is ready!"
echo "Backend PID: $BACKEND_PID"
echo "Nginx PID: $NGINX_PID"

# Wait for either process to exit
wait -n $BACKEND_PID $NGINX_PID

# If we get here, one of the processes has exited
echo "One of the processes has exited, shutting down..."
shutdown
