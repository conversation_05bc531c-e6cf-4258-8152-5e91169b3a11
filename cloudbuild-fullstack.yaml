# Google Cloud Build configuration for full-stack GCP deployment
steps:
  # Install frontend dependencies
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['install']

  # Build frontend with GCP-only configuration
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    env:
      - 'VITE_API_URL=https://insomnia-app-${_REGION}-${_PROJECT_HASH}.a.run.app'
      - 'VITE_DEPLOYMENT_TYPE=gcp-only'
      - 'VITE_ENVIRONMENT=production'

  # Build backend Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/insomnia-app:$BUILD_ID', '-f', 'Dockerfile.fullstack', '.']

  # Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/insomnia-app:$BUILD_ID']

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'insomnia-app'
      - '--image'
      - 'gcr.io/$PROJECT_ID/insomnia-app:$BUILD_ID'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '4Gi'
      - '--cpu'
      - '4'
      - '--timeout'
      - '3600'
      - '--max-instances'
      - '20'
      - '--set-env-vars'
      - 'CLOUD_STORAGE_ENABLED=true,GCS_BUCKET_NAME=${_GCS_BUCKET_NAME},GCP_PROJECT_ID=$PROJECT_ID'

# Substitution variables
substitutions:
  _GCS_BUCKET_NAME: 'insomnia-video-storage'
  _REGION: 'us-central1'
  _PROJECT_HASH: 'xxxxx-uc'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Build timeout
timeout: '1800s'
